#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CloudStorageClient 使用示例
演示如何使用 CloudStorageClient 进行文件上传下载和管理
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.popclients.cloud_storage_client import (
    CloudStorageClient,
    CloudStorageClientError,
    get_cloud_storage_client
)


def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("CloudStorageClient 基本使用示例")
    print("=" * 60)
    
    try:
        # 创建客户端实例
        print("1. 创建客户端实例...")
        client = CloudStorageClient(
            endpoint="ecd-inner-share.cn-hangzhou.aliyuncs.com",
            connect_timeout=10000,
            read_timeout=30000
        )
        
        # 获取客户端信息
        client_info = client.get_client_info()
        print(f"客户端信息: {client_info}")
        
        print("✅ 客户端创建成功")
        
    except CloudStorageClientError as e:
        print(f"❌ 客户端创建失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


def example_singleton_usage():
    """单例模式使用示例"""
    print("\n" + "=" * 60)
    print("CloudStorageClient 单例模式示例")
    print("=" * 60)
    
    try:
        # 获取单例实例
        print("1. 获取单例实例...")
        client1 = get_cloud_storage_client()
        client2 = get_cloud_storage_client()
        
        # 验证是同一个实例
        print(f"client1 == client2: {client1 is client2}")
        print(f"client1: {client1}")
        print(f"client2: {client2}")
        
        print("✅ 单例模式工作正常")
        
    except CloudStorageClientError as e:
        print(f"❌ 单例获取失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


def example_file_upload_workflow():
    """文件上传工作流示例"""
    print("\n" + "=" * 60)
    print("CloudStorageClient 文件上传工作流示例")
    print("=" * 60)
    
    try:
        # 获取客户端
        client = get_cloud_storage_client()
        
        # 示例参数
        file_path = "/user/documents/example.pdf"
        product_type = "AIPC"
        user_ali_uid = "123456789"
        wy_drive_owner_id = "owner_123"
        
        print("1. 预上传文件...")
        print(f"文件路径: {file_path}")
        print(f"产品类型: {product_type}")
        print(f"用户UID: {user_ali_uid}")
        print(f"云盘所有者ID: {wy_drive_owner_id}")
        
        # 注意：这里只是演示API调用，实际使用时需要有效的认证和参数
        print("⚠️  注意：这是API调用演示，需要有效的认证配置才能成功")
        
        # 模拟预上传调用
        try:
            pre_upload_response = client.pre_upload_file(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id
            )
            print("✅ 预上传成功")
            print(f"响应: {pre_upload_response}")
        except Exception as e:
            print(f"⚠️  预上传调用失败（预期的，因为缺少有效认证）: {e}")
        
        print("\n2. 完成文件上传...")
        try:
            complete_response = client.complete_upload_file(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id
            )
            print("✅ 上传完成")
            print(f"响应: {complete_response}")
        except Exception as e:
            print(f"⚠️  完成上传调用失败（预期的，因为缺少有效认证）: {e}")
        
    except CloudStorageClientError as e:
        print(f"❌ 上传工作流失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


def example_file_download():
    """文件下载示例"""
    print("\n" + "=" * 60)
    print("CloudStorageClient 文件下载示例")
    print("=" * 60)
    
    try:
        # 获取客户端
        client = get_cloud_storage_client()
        
        # 示例参数
        file_path = "/user/documents/example.pdf"
        product_type = "AIPC"
        user_ali_uid = "123456789"
        wy_drive_owner_id = "owner_123"
        
        print("1. 获取文件下载URL...")
        print(f"文件路径: {file_path}")
        
        try:
            download_response = client.get_download_url_by_path(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id
            )
            print("✅ 获取下载URL成功")
            print(f"响应: {download_response}")
            
            # 如果有响应体，提取下载URL
            if hasattr(download_response, 'body') and download_response.body:
                if hasattr(download_response.body, 'download_url'):
                    download_url = download_response.body.download_url
                    print(f"下载链接: {download_url}")
                    
        except Exception as e:
            print(f"⚠️  获取下载URL失败（预期的，因为缺少有效认证）: {e}")
        
    except CloudStorageClientError as e:
        print(f"❌ 下载示例失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


def example_list_files():
    """文件列表查询示例"""
    print("\n" + "=" * 60)
    print("CloudStorageClient 文件列表查询示例")
    print("=" * 60)
    
    try:
        # 获取客户端
        client = get_cloud_storage_client()
        
        # 示例参数
        user_ali_uid = "123456789"
        wy_drive_owner_id = "owner_123"
        
        print("1. 查询无影云盘文件...")
        print(f"用户UID: {user_ali_uid}")
        print(f"云盘所有者ID: {wy_drive_owner_id}")
        
        try:
            files_response = client.describe_wy_drive_file_flat(
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id
            )
            print("✅ 查询文件成功")
            print(f"响应: {files_response}")
            
            # 如果有响应体，提取文件列表
            if hasattr(files_response, 'body') and files_response.body:
                if hasattr(files_response.body, 'wy_drive_files'):
                    files = files_response.body.wy_drive_files
                    print(f"找到 {len(files) if files else 0} 个文件")
                    
                    if files:
                        for i, file_info in enumerate(files[:5]):  # 只显示前5个
                            print(f"  文件 {i+1}: {getattr(file_info, 'file_name', 'Unknown')}")
                            
        except Exception as e:
            print(f"⚠️  查询文件失败（预期的，因为缺少有效认证）: {e}")
        
    except CloudStorageClientError as e:
        print(f"❌ 文件列表查询失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


async def example_async_operations():
    """异步操作示例"""
    print("\n" + "=" * 60)
    print("CloudStorageClient 异步操作示例")
    print("=" * 60)
    
    try:
        # 获取客户端
        client = get_cloud_storage_client()
        
        # 示例参数
        file_path = "/user/documents/async_example.pdf"
        product_type = "AIPC"
        user_ali_uid = "123456789"
        wy_drive_owner_id = "owner_123"
        
        print("1. 异步预上传文件...")
        try:
            pre_upload_response = await client.pre_upload_file_async(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id
            )
            print("✅ 异步预上传成功")
            print(f"响应: {pre_upload_response}")
        except Exception as e:
            print(f"⚠️  异步预上传失败（预期的，因为缺少有效认证）: {e}")
        
        print("\n2. 异步获取下载URL...")
        try:
            download_response = await client.get_download_url_by_path_async(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id
            )
            print("✅ 异步获取下载URL成功")
            print(f"响应: {download_response}")
        except Exception as e:
            print(f"⚠️  异步获取下载URL失败（预期的，因为缺少有效认证）: {e}")
        
        print("\n3. 异步查询文件...")
        try:
            files_response = await client.describe_wy_drive_file_flat_async(
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id
            )
            print("✅ 异步查询文件成功")
            print(f"响应: {files_response}")
        except Exception as e:
            print(f"⚠️  异步查询文件失败（预期的，因为缺少有效认证）: {e}")
        
    except CloudStorageClientError as e:
        print(f"❌ 异步操作失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


def main():
    """主函数"""
    print("CloudStorageClient 使用示例")
    print("这些示例展示了如何使用 CloudStorageClient 进行各种操作")
    print("注意：实际使用时需要有效的阿里云认证配置")
    
    # 基本使用
    example_basic_usage()
    
    # 单例模式
    example_singleton_usage()
    
    # 文件上传工作流
    example_file_upload_workflow()
    
    # 文件下载
    example_file_download()
    
    # 文件列表查询
    example_list_files()
    
    # 异步操作
    print("\n运行异步操作示例...")
    asyncio.run(example_async_operations())
    
    print("\n" + "=" * 60)
    print("🎉 所有示例运行完成！")
    print("=" * 60)
    print("\n📝 使用说明:")
    print("1. 这些示例展示了 CloudStorageClient 的基本用法")
    print("2. 实际使用时需要配置有效的阿里云认证信息")
    print("3. 需要确保 RAM 角色和权限配置正确")
    print("4. 建议在测试环境中先验证功能")


if __name__ == "__main__":
    main()
