#!/usr/bin/env python3
"""
Circus异常分析工具
用于分析Circus如何判断异常以及通过日志定位问题
"""

import os
import sys
import subprocess
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class CircusAnalyzer:
    """Circus异常分析器"""
    
    def __init__(self):
        self.circus_config = "/home/<USER>/wuying-alpha-service/conf/circus.ini"
        self.circus_pid_file = "/home/<USER>/circus.pid"
        self.app_log = "/home/<USER>/wuying-alpha-service/logs/application.log"
        self.error_log = "/home/<USER>/wuying-alpha-service/logs/error.log"
        
    def analyze_circus_config(self):
        """分析Circus配置"""
        print("🔍 分析Circus配置...")
        
        if not os.path.exists(self.circus_config):
            print(f"❌ Circus配置文件不存在: {self.circus_config}")
            return
        
        try:
            with open(self.circus_config, 'r') as f:
                content = f.read()
            
            print("📋 Circus配置分析:")
            
            # 解析配置
            lines = content.split('\n')
            current_section = None
            config_data = {}
            
            for line in lines:
                line = line.strip()
                if line.startswith('[') and line.endswith(']'):
                    current_section = line[1:-1]
                    config_data[current_section] = {}
                elif '=' in line and current_section:
                    key, value = line.split('=', 1)
                    config_data[current_section][key.strip()] = value.strip()
            
            # 显示关键配置
            if 'circus' in config_data:
                circus_config = config_data['circus']
                print(f"   🔄 check_delay: {circus_config.get('check_delay', '未设置')}秒")
                print(f"   📍 endpoint: {circus_config.get('endpoint', '未设置')}")
                print(f"   📄 pidfile: {circus_config.get('pidfile', '未设置')}")
            
            if 'watcher:main' in config_data:
                watcher_config = config_data['watcher:main']
                print(f"   🐍 cmd: {watcher_config.get('cmd', '未设置')}")
                print(f"   📁 working_dir: {watcher_config.get('working_dir', '未设置')}")
                print(f"   🛑 stop_signal: {watcher_config.get('stop_signal', '未设置')}")
                print(f"   📝 stdout_stream: {watcher_config.get('stdout_stream.filename', '未设置')}")
                print(f"   ❌ stderr_stream: {watcher_config.get('stderr_stream.filename', '未设置')}")
            
            # 分析异常判断机制
            print(f"\n🔍 Circus异常判断机制:")
            print(f"   1. 进程存活检查: 每{circus_config.get('check_delay', '5')}秒检查一次")
            print(f"   2. 进程退出检测: 如果进程不存在，自动重启")
            print(f"   3. 信号处理: 使用{watcher_config.get('stop_signal', 'SIGTERM')}停止进程")
            print(f"   4. 日志记录: 标准输出和错误输出分别记录")
            
        except Exception as e:
            print(f"❌ 分析Circus配置失败: {e}")
    
    def check_circus_status(self):
        """检查Circus状态"""
        print(f"\n🔍 检查Circus状态...")
        
        # 检查Circus进程
        try:
            result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
            circus_processes = [line for line in result.stdout.split('\n') if 'circusd' in line]
            
            if circus_processes:
                print("✅ Circus进程正在运行:")
                for proc in circus_processes:
                    print(f"   {proc}")
            else:
                print("❌ Circus进程未运行")
                
            # 检查PID文件
            if os.path.exists(self.circus_pid_file):
                with open(self.circus_pid_file, 'r') as f:
                    circus_pid = f.read().strip()
                print(f"📄 Circus PID文件: {circus_pid}")
            else:
                print("❌ Circus PID文件不存在")
                
        except Exception as e:
            print(f"❌ 检查Circus状态失败: {e}")
    
    def analyze_process_restarts(self):
        """分析进程重启情况"""
        print(f"\n🔍 分析进程重启情况...")
        
        try:
            # 查找启动日志
            if os.path.exists(self.app_log):
                with open(self.app_log, 'r') as f:
                    lines = f.readlines()
                
                # 查找启动和重启相关的日志
                startup_lines = []
                restart_lines = []
                
                for line in lines:
                    if "启动Pythonic Alpha Service" in line:
                        startup_lines.append(line.strip())
                    elif "circus" in line.lower() and ("restart" in line.lower() or "start" in line.lower()):
                        restart_lines.append(line.strip())
                
                print(f"📊 启动日志统计:")
                print(f"   - 总启动次数: {len(startup_lines)}")
                print(f"   - Circus重启相关: {len(restart_lines)}")
                
                if startup_lines:
                    print(f"\n📅 最近的启动记录:")
                    for line in startup_lines[-5:]:  # 显示最近5次
                        print(f"   {line}")
                
                if restart_lines:
                    print(f"\n🔄 Circus重启记录:")
                    for line in restart_lines[-5:]:  # 显示最近5次
                        print(f"   {line}")
                        
        except Exception as e:
            print(f"❌ 分析进程重启失败: {e}")
    
    def analyze_error_patterns(self):
        """分析错误模式"""
        print(f"\n🔍 分析错误模式...")
        
        error_patterns = {
            "进程退出": ["exit", "killed", "terminated", "segmentation fault"],
            "内存问题": ["out of memory", "memory", "malloc", "free"],
            "网络问题": ["connection", "timeout", "network", "socket"],
            "文件系统": ["no space", "disk full", "permission denied"],
            "Python异常": ["exception", "traceback", "error"],
            "RocketMQ": ["rocketmq", "RocketMQ", "mq"],
            "异步回调": ["callback", "future", "cancelled"],
            "启动脚本": ["启动后脚本执行失败", "startup script"]
        }
        
        error_stats = {category: 0 for category in error_patterns}
        recent_errors = []
        
        # 分析应用日志
        if os.path.exists(self.app_log):
            try:
                with open(self.app_log, 'r') as f:
                    lines = f.readlines()
                
                # 分析最近1小时的日志
                cutoff_time = datetime.now() - timedelta(hours=1)
                
                for line in lines:
                    try:
                        # 解析时间戳
                        timestamp_str = line.split(' | ')[0]
                        log_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        
                        if log_time >= cutoff_time:
                            line_lower = line.lower()
                            
                            # 统计错误模式
                            for category, patterns in error_patterns.items():
                                for pattern in patterns:
                                    if pattern.lower() in line_lower:
                                        error_stats[category] += 1
                                        recent_errors.append(line.strip())
                                        break
                    except:
                        continue
                        
            except Exception as e:
                print(f"❌ 分析应用日志失败: {e}")
        
        # 分析错误日志
        if os.path.exists(self.error_log):
            try:
                with open(self.error_log, 'r') as f:
                    lines = f.readlines()
                
                for line in lines:
                    try:
                        timestamp_str = line.split(' | ')[0]
                        log_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        
                        if log_time >= cutoff_time:
                            line_lower = line.lower()
                            
                            for category, patterns in error_patterns.items():
                                for pattern in patterns:
                                    if pattern.lower() in line_lower:
                                        error_stats[category] += 1
                                        recent_errors.append(line.strip())
                                        break
                    except:
                        continue
                        
            except Exception as e:
                print(f"❌ 分析错误日志失败: {e}")
        
        # 显示错误统计
        print(f"📈 最近1小时错误统计:")
        for category, count in error_stats.items():
            if count > 0:
                print(f"   - {category}: {count}次")
        
        # 显示最近的错误
        if recent_errors:
            print(f"\n🚨 最近的错误消息:")
            for error in recent_errors[-10:]:  # 显示最近10条
                print(f"   {error}")
    
    def check_process_health(self):
        """检查进程健康状态"""
        print(f"\n🔍 检查进程健康状态...")
        
        try:
            # 检查Python进程
            result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
            python_processes = [line for line in result.stdout.split('\n') if 'python3' in line and 'start_service.py' in line]
            
            if python_processes:
                print(f"✅ 发现 {len(python_processes)} 个Python进程:")
                for proc in python_processes:
                    parts = proc.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        print(f"   PID: {pid}, 进程: {proc}")
                        
                        # 检查进程状态
                        try:
                            status_result = subprocess.run(["ps", "-p", pid, "-o", "state="], capture_output=True, text=True)
                            state = status_result.stdout.strip()
                            print(f"   状态: {state}")
                        except:
                            pass
            else:
                print("❌ 没有找到Python服务进程")
                
            # 检查端口监听
            try:
                port_result = subprocess.run(["netstat", "-tlnp"], capture_output=True, text=True)
                port_8000 = [line for line in port_result.stdout.split('\n') if ':8000' in line]
                
                if port_8000:
                    print(f"✅ 端口8000正在监听:")
                    for line in port_8000:
                        print(f"   {line}")
                else:
                    print("❌ 端口8000未监听")
                    
            except Exception as e:
                print(f"❌ 检查端口失败: {e}")
                
        except Exception as e:
            print(f"❌ 检查进程健康状态失败: {e}")
    
    def generate_recommendations(self):
        """生成建议"""
        print(f"\n💡 Circus异常处理建议:")
        print(f"   1. 监控关键指标:")
        print(f"      - 进程存活状态")
        print(f"      - 内存使用情况")
        print(f"      - 网络连接状态")
        print(f"      - 磁盘空间")
        print(f"   2. 日志分析:")
        print(f"      - 定期检查错误日志")
        print(f"      - 分析重启模式")
        print(f"      - 监控异常频率")
        print(f"   3. 配置优化:")
        print(f"      - 调整check_delay参数")
        print(f"      - 设置合理的重启策略")
        print(f"      - 配置日志轮转")
        print(f"   4. 预防措施:")
        print(f"      - 定期清理日志文件")
        print(f"      - 监控系统资源")
        print(f"      - 设置告警机制")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 Circus异常分析报告")
        print("=" * 60)
        print(f"📅 分析时间: {datetime.now()}")
        print("=" * 60)
        
        self.analyze_circus_config()
        self.check_circus_status()
        self.analyze_process_restarts()
        self.analyze_error_patterns()
        self.check_process_health()
        self.generate_recommendations()
        
        print("\n" + "=" * 60)
        print("✅ 分析完成！")
        print("\n📋 关键发现:")
        print("1. Circus通过check_delay定期检查进程状态")
        print("2. 进程退出、信号异常、资源不足都会触发重启")
        print("3. 所有重启都会记录在日志中")
        print("4. 可以通过错误模式分析定位根本原因")

def main():
    """主函数"""
    analyzer = CircusAnalyzer()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "config":
            analyzer.analyze_circus_config()
        elif sys.argv[1] == "status":
            analyzer.check_circus_status()
        elif sys.argv[1] == "restarts":
            analyzer.analyze_process_restarts()
        elif sys.argv[1] == "errors":
            analyzer.analyze_error_patterns()
        elif sys.argv[1] == "health":
            analyzer.check_process_health()
        else:
            print("用法:")
            print("  python circus_analysis.py              # 完整分析")
            print("  python circus_analysis.py config       # 分析配置")
            print("  python circus_analysis.py status       # 检查状态")
            print("  python circus_analysis.py restarts     # 分析重启")
            print("  python circus_analysis.py errors       # 分析错误")
            print("  python circus_analysis.py health       # 检查健康状态")
    else:
        analyzer.run_full_analysis()

if __name__ == "__main__":
    main() 