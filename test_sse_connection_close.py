#!/usr/bin/env python3
"""
测试SSE连接关闭的日志输出
"""

import asyncio
import json
import time
from datetime import datetime
from loguru import logger

# 模拟SSE管理器
class MockSSEManager:
    def __init__(self):
        self.sse_connections = {}
        self.heartbeat_tasks = {}
    
    async def create_sse_stream(self, session_id: str):
        """模拟SSE流创建"""
        logger.info(f"[MockSSEManager] 开始创建SSE流: session_id={session_id}")
        
        # 创建消息队列
        message_queue = asyncio.Queue()
        self.sse_connections[session_id] = message_queue
        
        heartbeat_task = None
        try:
            # 模拟心跳任务
            heartbeat_task = asyncio.create_task(self._mock_heartbeat(message_queue))
            self.heartbeat_tasks[session_id] = heartbeat_task
            
            logger.info(f"[MockSSEManager] SSE流创建完成: session_id={session_id}")
            
            # 模拟消息循环
            async for event in self._mock_message_loop(message_queue, session_id):
                yield event
                
        except Exception as e:
            logger.error(f"[MockSSEManager] SSE流异常: session_id={session_id}, error={e}")
            raise
        finally:
            logger.info(f"[MockSSEManager] 开始清理SSE连接: session_id={session_id}")
            
            # 取消心跳任务
            if heartbeat_task and not heartbeat_task.done():
                heartbeat_task.cancel()
                try:
                    await heartbeat_task
                except asyncio.CancelledError:
                    logger.debug(f"[MockSSEManager] 心跳任务已取消: session_id={session_id}")
                except Exception as e:
                    logger.error(f"[MockSSEManager] 等待心跳任务结束时出错: session_id={session_id}, error={e}")
            
            # 清理连接和任务记录
            self._cleanup_session_connection(session_id)
            self._cleanup_session_heartbeat_task(session_id)
            
            logger.info(f"[MockSSEManager] SSE流和HTTP连接已完全关闭: session_id={session_id}")
            logger.info(f"[MockSSEManager] 清理后剩余连接数={len(self.sse_connections)}, 剩余连接: {list(self.sse_connections.keys())}")
    
    async def _mock_heartbeat(self, message_queue):
        """模拟心跳循环"""
        try:
            while True:
                await asyncio.sleep(3)
                heartbeat_data = json.dumps({"type": "heartbeat"})
                await message_queue.put(heartbeat_data)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"[MockSSEManager] 心跳异常: {e}")
    
    async def _mock_message_loop(self, message_queue, session_id):
        """模拟消息循环"""
        while True:
            try:
                message = await asyncio.wait_for(message_queue.get(), timeout=30.0)
                
                # 检查是否是关闭信号
                if isinstance(message, dict) and message.get("type") in ["close", "done"]:
                    logger.info(f"[MockSSEManager] 收到后端关闭信号，退出消息循环: session_id={session_id}, signal_type={message.get('type')}")
                    break
                
                # 正常消息处理
                if isinstance(message, dict):
                    message = json.dumps(message)
                
                yield f"data: {message}\n\n"
            except asyncio.TimeoutError:
                logger.warning(f"[MockSSEManager] SSE心跳超时，可能是前端断开连接: session_id={session_id}")
                break
            except Exception as e:
                logger.error(f"[MockSSEManager] SSE异常，可能是前端断开连接: session_id={session_id}, error={e}")
                break
    
    def _cleanup_session_connection(self, session_id):
        """清理session级别的连接"""
        if session_id in self.sse_connections:
            logger.debug(f"[MockSSEManager] 清理SSE连接: session_id={session_id}")
            del self.sse_connections[session_id]
            logger.debug(f"[MockSSEManager] SSE连接已清理: session_id={session_id}")
        else:
            logger.debug(f"[MockSSEManager] SSE连接不存在，无需清理: session_id={session_id}")
    
    def _cleanup_session_heartbeat_task(self, session_id):
        """清理session级别的心跳任务记录"""
        if session_id in self.heartbeat_tasks:
            heartbeat_task = self.heartbeat_tasks[session_id]
            if heartbeat_task and not heartbeat_task.done():
                heartbeat_task.cancel()
                logger.debug(f"[MockSSEManager] 清理时取消心跳任务: session_id={session_id}")
            del self.heartbeat_tasks[session_id]
            logger.debug(f"[MockSSEManager] 心跳任务已清理: session_id={session_id}")
        else:
            logger.debug(f"[MockSSEManager] 心跳任务不存在，无需清理: session_id={session_id}")
    
    def close_session_connection(self, session_id):
        """关闭Session的SSE连接"""
        try:
            logger.info(f"[MockSSEManager] 开始关闭Session连接: session_id={session_id}")
            
            # 取消心跳任务
            if session_id in self.heartbeat_tasks:
                heartbeat_task = self.heartbeat_tasks[session_id]
                if heartbeat_task and not heartbeat_task.done():
                    heartbeat_task.cancel()
                    logger.info(f"[MockSSEManager] 已取消心跳任务: session_id={session_id}")
                del self.heartbeat_tasks[session_id]
            
            # 向session连接发送关闭信号
            if session_id in self.sse_connections:
                message_queue = self.sse_connections[session_id]
                try:
                    asyncio.create_task(message_queue.put({
                        "type": "close",
                        "data": {"message": "Session completed"}
                    }))
                    logger.info(f"[MockSSEManager] 已向连接发送关闭信号: session_id={session_id}")
                except Exception as e:
                    logger.error(f"[MockSSEManager] 发送关闭信号失败: session_id={session_id}, error={e}")
            else:
                logger.warning(f"[MockSSEManager] Session连接不存在，无法发送关闭信号: session_id={session_id}")
                
        except Exception as e:
            logger.error(f"[MockSSEManager] 关闭Session连接失败: session_id={session_id}, error={e}")

async def test_backend_close():
    """测试后端主动关闭连接"""
    logger.info("=== 测试后端主动关闭连接 ===")
    
    sse_manager = MockSSEManager()
    session_id = "test_session_backend_close"
    
    # 创建SSE流
    async for event in sse_manager.create_sse_stream(session_id):
        logger.info(f"收到事件: {event}")
        # 模拟后端主动关闭
        if "heartbeat" in event:
            logger.info("模拟后端主动关闭连接")
            sse_manager.close_session_connection(session_id)
            break

async def test_frontend_close():
    """测试前端断开连接"""
    logger.info("=== 测试前端断开连接 ===")
    
    sse_manager = MockSSEManager()
    session_id = "test_session_frontend_close"
    
    # 创建SSE流
    try:
        async for event in sse_manager.create_sse_stream(session_id):
            logger.info(f"收到事件: {event}")
            # 模拟前端断开连接（通过超时）
            if "heartbeat" in event:
                logger.info("模拟前端断开连接（超时）")
                # 不发送任何消息，让连接超时
                await asyncio.sleep(35)  # 超过30秒超时
                break
    except Exception as e:
        logger.error(f"前端断开连接测试异常: {e}")

async def test_exception_close():
    """测试异常关闭连接"""
    logger.info("=== 测试异常关闭连接 ===")
    
    sse_manager = MockSSEManager()
    session_id = "test_session_exception_close"
    
    # 创建SSE流
    try:
        async for event in sse_manager.create_sse_stream(session_id):
            logger.info(f"收到事件: {event}")
            # 模拟异常
            if "heartbeat" in event:
                logger.info("模拟异常关闭连接")
                raise Exception("模拟异常")
    except Exception as e:
        logger.error(f"异常关闭连接测试: {e}")

async def main():
    """主测试函数"""
    logger.info("开始SSE连接关闭测试")
    
    # 测试后端主动关闭
    await test_backend_close()
    await asyncio.sleep(2)
    
    # 测试前端断开连接
    await test_frontend_close()
    await asyncio.sleep(2)
    
    # 测试异常关闭
    await test_exception_close()
    
    logger.info("SSE连接关闭测试完成")

if __name__ == "__main__":
    # 配置日志格式
    logger.remove()
    logger.add(
        lambda msg: print(msg, end=""),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
        level="DEBUG"
    )
    
    asyncio.run(main()) 