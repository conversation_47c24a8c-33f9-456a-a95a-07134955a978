"""
AIPPT相关API模型
请求和响应的数据模型定义
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any


# ==================== 获取认证code相关模型 ====================


class GetPPTAuthCodeResponse(BaseModel):
    """获取PPT认证code响应"""

    code: str = Field(..., description="认证code")
    time_expire: str = Field(..., description="认证code有效期")
    api_key: str = Field(..., description="AIPPT API Key")
    channel: str = Field(..., description="渠道标识")


class AIPPTTokenResponse(BaseModel):
    """AIPPT token响应"""

    token: str = Field(..., description="AIPPT token")
    time_expire: str = Field(..., description="AIPPT token有效期")



# ==================== 保存PPT相关模型 ====================


class SavePPTRequest(BaseModel):
    """保存PPT请求"""

    session_id: str = Field(..., description="会话ID")
    ppt_id: str = Field(..., description="PPT作品ID")
    format: str = Field(default="ppt", description="导出格式，支持: png|jpeg|pdf|ppt")

class BindPPTToSessionRequest(BaseModel):
    """绑定PPT到会话请求"""

    session_id: str = Field(..., description="会话ID")
    ppt_id: str = Field(..., description="PPT作品ID")


# ==================== 下载PPT相关模型 ====================


class DownloadPPTRequest(BaseModel):
    """下载PPT请求"""

    session_id: str = Field(..., description="会话ID")
    ppt_id: str = Field(..., description="PPT作品ID")


class DownloadPPTResponse(BaseModel):
    """下载PPT响应"""

    download_url: str = Field(..., description="PPT下载URL")


# ==================== 删除PPT相关模型 ====================


class DeletePPTRequest(BaseModel):
    """删除PPT请求"""

    ppt_id: str = Field(..., description="PPT作品ID")


# ==================== 获取PPT封面图相关模型 ====================


class GetPPTThumbnailRequest(BaseModel):
    """获取PPT封面图请求"""

    ppt_id: str = Field(..., description="PPT作品ID")


# ==================== 通用错误响应模型 ====================


class PPTErrorResponse(BaseModel):
    """PPT服务错误响应"""

    error_code: str = Field(..., description="错误码")
    error_message: str = Field(..., description="错误信息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
