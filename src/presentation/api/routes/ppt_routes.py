"""
PPT相关API路由
处理PPT相关的HTTP接口，包括认证、生成、保存和下载等功能
"""

from datetime import datetime
from src.domain.utils.check_utils import CheckUtils
from fastapi import APIRouter, HTTPException, Depends
from loguru import logger

from src.application.ppt_api_models import (
    BindPPTToSessionRequest,
    GetPPTThumbnailRequest,
    SavePPTRequest,
    DownloadPPTRequest,
    DeletePPTRequest,
)
from src.domain.services.ppt_service import ppt_service, PPTServiceError
from src.domain.services.auth_service import AuthContext, require_auth
from src.presentation.api.dependencies.api_common_utils import (
    package_api_result,
    handle_exception,
    get_request_id_dependency,
)
from src.presentation.api.dependencies.common_params import CommonParams


router = APIRouter(prefix="/api/aippt", tags=["ppt"])


@router.get("/code/grant")
def get_ppt_auth_code(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """
    获取PPT认证code
    """
    try:
        # 检查参数
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")

        logger.info(f"[PPT API] 获取认证code: ali_uid={current_user.ali_uid}")
        response = ppt_service.get_ppt_auth_code(
            ali_uid=current_user.ali_uid,
        )

        return package_api_result(
            data=response,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/session/bind")
def bind_ppt_to_session(
    request: BindPPTToSessionRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """
    绑定PPT到会话
    """
    try:
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")
        CheckUtils.parameter_not_null(request.session_id, "session_id")

        # 调用业务服务
        ppt_service.bind_ppt_to_session(
            session_id=request.session_id, ppt_id=request.ppt_id
        )

        return package_api_result(
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/thumbnail/get")
def get_ppt_thumbnail(
    request: GetPPTThumbnailRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """
    获取PPT缩略图
    """
    try:
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")
        CheckUtils.parameter_not_null(request.ppt_id, "ppt_id")

        # 调用业务服务
        response = ppt_service.get_ppt_thumbnail(ppt_id=request.ppt_id)

        return package_api_result(
            data=response,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/save")
def save_ppt(
    request: SavePPTRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """
    保存PPT
    """
    try:
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")
        CheckUtils.parameter_not_null(request.ppt_id, "ppt_id")

        # 调用业务服务
        ppt_service.save_ppt(ppt_id=request.ppt_id, format=request.format)

        return package_api_result(
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/download")
def download_ppt(
    request: DownloadPPTRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """
    下载PPT
    """
    try:
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")
        CheckUtils.parameter_not_null(request.ppt_id, "ppt_id")

        # 调用业务服务
        response = ppt_service.save_ppt(ppt_id=request.ppt_id)

        return package_api_result(
            data=response,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/delete")
def delete_ppt(
    request: DeletePPTRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """
    删除PPT
    """
    try:
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")
        CheckUtils.parameter_not_null(request.ppt_id, "ppt_id")

        # 调用业务服务
        success = ppt_service.delete_ppt(ppt_id=request.ppt_id)

        return package_api_result(
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


# ==================== 健康检查接口 ====================


@router.get("/health")
def ppt_service_health():
    """
    PPT服务健康检查
    """
    try:
        # 简单的健康检查
        return {
            "status": "healthy",
            "service": "ppt_service",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
        }
    except Exception as e:
        logger.error(f"[PPT API] 健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail="PPT服务不可用")
