#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件业务服务
负责文件上传、下载、管理等业务逻辑
"""

import json
import uuid
import traceback
import asyncio
from application.file_api_models import DownloadUrlsResponse, FileInfoResponse, SessionFilesResponse
import requests
import threading
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
from loguru import logger
from io import BytesIO

from ...infrastructure.database.repositories.file_repository import file_repository
from ...infrastructure.database.models.file_models import AlphaFile, FileType, UploadStatus
from ...infrastructure.database.models.auth_models import ResourceType
from ...infrastructure.oss.oss_service import oss_service
from .auth_service import auth_service, AuthContext
from ...popclients.rag_client import create_rag_client, RagClientError
from src.domain.utils.check_utils import CheckUtils
from src.domain.utils.next_token_utils import NextTokenUtils


class FileProcessError(Exception):
    """文件处理异常"""
    pass


class FileUploadProgress:
    """文件上传进度跟踪器"""

    def __init__(self, file_id: int):
        self.file_id = file_id
        self.last_progress = 0

    def __call__(self, consumed_bytes: int, total_bytes: int):
        """OSS进度回调函数"""
        if total_bytes > 0:
            progress = int(100 * consumed_bytes / total_bytes)
            # 只有进度变化时才更新数据库，避免频繁更新
            if progress != self.last_progress:
                file_repository.update_file_progress(self.file_id, progress)
                self.last_progress = progress
                logger.debug(f"[FileService] 文件上传进度: file_id={self.file_id}, progress={progress}%")


class FileService:
    """文件业务服务 - 处理文件相关的业务逻辑"""
    
    def __init__(self):
        self.file_repository = file_repository
        self.oss_service = oss_service
        # 创建线程池用于异步处理文件上传和RAG解析
        self._thread_pool = ThreadPoolExecutor(max_workers=5, thread_name_prefix="FileService")
        self._shutdown_lock = threading.Lock()
    

    def upload_file(
        self,
        context: AuthContext,
        file_content: BytesIO,
        original_filename: str,
        session_id: str,
        file_type: str = FileType.SESSION_FILE.value,
        content_type: Optional[str] = None
    ) -> Tuple[AlphaFile, bool]:
        """
        上传文件（异步处理）

        Args:
            context: 认证上下文
            file_content: 文件内容流
            original_filename: 原始文件名
            session_id: 会话ID
            file_type: 文件类型
            content_type: 文件MIME类型

        Returns:
            Tuple[AlphaFile, bool]: (文件对象, 是否创建成功)
        """
        logger.info(f"[FileService] 开始上传文件: {original_filename}, session_id={session_id}, user={context.user_key}")

        try:
            # 读取文件数据
            file_data = file_content.read()
            file_size = len(file_data)

            # 验证文件
            self._validate_file(file_data, original_filename)

            # 生成OSS对象名称
            object_name = self._generate_object_name(original_filename, file_type, str(context.ali_uid))
            
            # 获取OSS配置
            oss_config = self.oss_service.config
            
            # 先创建数据库记录
            file_obj = self.file_repository.create_file(
                title=original_filename,
                oss_bucket=oss_config.bucket_name,
                oss_object_name=object_name,
                file_type=file_type,
                session_id=session_id,
                ali_uid=context.ali_uid,
                wy_id=context.wy_id,
                file_size=file_size,
                content_type=content_type
            )

            # 立即返回文件对象，异步处理上传和RAG解析
            logger.info(f"[FileService] 文件记录创建成功，开始异步处理: file_id={file_obj.id}")

            # 提交异步任务
            self._submit_async_upload_task(
                file_obj=file_obj,
                file_data=file_data,
                object_name=object_name,
                content_type=content_type,
                file_type=file_type,
                context=context,
                original_filename=original_filename,
                file_size=file_size
            )

            return file_obj, True

        except ValueError as e:
            logger.error(f"[FileService] 文件验证失败: {original_filename}, error={e}")
            raise
        except Exception as e:
            logger.error(f"[FileService] 文件上传服务异常: {original_filename}, error={e}", exc_info=True)
            raise

    def _submit_async_upload_task(
        self,
        file_obj: AlphaFile,
        file_data: bytes,
        object_name: str,
        content_type: Optional[str],
        file_type: str,
        context: AuthContext,
        original_filename: str,
        file_size: int
    ) -> None:
        """
        提交异步上传任务

        Args:
            file_obj: 文件对象
            file_data: 文件数据
            object_name: OSS对象名称
            content_type: 文件MIME类型
            file_type: 文件类型
            context: 认证上下文
            original_filename: 原始文件名
            file_size: 文件大小
        """
        try:
            with self._shutdown_lock:
                if self._thread_pool._shutdown:
                    logger.error(f"[FileService] 线程池已关闭，无法提交异步任务: file_id={file_obj.id}")
                    self.file_repository.mark_file_failed(file_obj.id, "服务正在关闭")  # type: ignore
                    return

                # 提交异步任务
                future = self._thread_pool.submit(
                    self._async_upload_and_process,
                    file_obj,
                    file_data,
                    object_name,
                    content_type,
                    file_type,
                    context,
                    original_filename,
                    file_size
                )

                # 添加完成回调
                future.add_done_callback(
                    lambda f: self._handle_async_task_completion(f, file_obj.id) # type: ignore
                )

                logger.info(f"[FileService] 异步上传任务已提交: file_id={file_obj.id}")

        except Exception as e:
            logger.error(f"[FileService] 提交异步任务失败: file_id={file_obj.id}, error={e}", exc_info=True)
            self.file_repository.mark_file_failed(file_obj.id, f"提交异步任务失败: {str(e)}") # type: ignore

    def _async_upload_and_process(
        self,
        file_obj: AlphaFile,
        file_data: bytes,
        object_name: str,
        content_type: Optional[str],
        file_type: str,
        context: AuthContext,
        original_filename: str,
        file_size: int
    ) -> None:
        """
        异步执行文件上传和处理

        Args:
            file_obj: 文件对象
            file_data: 文件数据
            object_name: OSS对象名称
            content_type: 文件MIME类型
            file_type: 文件类型
            context: 认证上下文
            original_filename: 原始文件名
            file_size: 文件大小
        """
        logger.info(f"[FileService] 开始异步处理文件: file_id={file_obj.id}, filename={original_filename}")

        try:
            # 创建进度跟踪器
            progress_tracker = FileUploadProgress(file_id=int(file_obj.id))

            # 执行上传（使用upload_bytes方法，传入真正的进度回调）
            upload_result = self.oss_service.upload_bytes(
                data=file_data,
                key=object_name,
                content_type=content_type,
                progress_callback=progress_tracker
            )

            if upload_result.success:
                logger.info(f"[FileService] 文件上传成功: file_id={file_obj.id}")

                # 注册资源到鉴权系统
                try:
                    auth_service.register_resource(
                        context=context,
                        resource_type=ResourceType.FILE,
                        resource_id=str(file_obj.id),
                        resource_name=original_filename,
                        is_public=False
                    )
                except Exception as e:
                    logger.error(f"[FileService] 资源注册失败: file_id={file_obj.id}, error={e}")
                    # 资源注册失败不影响文件上传流程

                # 根据文件类型处理后续流程
                if file_type == FileType.SESSION_FILE.value:
                    # SESSION_FILE: 先标记为分析中，然后触发RAG解析
                    logger.info(f"[FileService] 会话文件上传完成，开始RAG解析流程: {original_filename} -> {object_name}")

                    analyzing_success = self.file_repository.mark_file_analyzing(file_obj.id)
                    if analyzing_success:
                        # 获取最新的文件对象并触发RAG解析
                        updated_file = self.file_repository.get_file_by_id(file_obj.id)
                        if updated_file:
                            self._parse_document_with_rag(updated_file, context)
                        else:
                            logger.error(f"[FileService] 获取文件对象失败: file_id={file_obj.id}")
                            self.file_repository.mark_file_failed(file_obj.id, "获取文件对象失败")
                    else:
                        logger.error(f"[FileService] 标记文件为分析中状态失败: file_id={file_obj.id}")
                        self.file_repository.mark_file_failed(file_obj.id, "标记分析状态失败")
                else:
                    # 其他文件类型：直接标记为完成
                    self.file_repository.mark_file_completed(file_obj.id, file_size)
                    logger.info(f"[FileService] 非会话文件上传完成: {original_filename} -> {object_name}")
            else:
                # 标记文件上传失败
                error_msg = upload_result.error_message or "OSS上传失败"
                self.file_repository.mark_file_failed(file_obj.id, error_msg)
                logger.error(f"[FileService] 文件上传失败: {original_filename}, error={error_msg}")

        except Exception as e:
            # 标记文件上传失败
            error_msg = f"异步处理异常: {str(e)}"
            self.file_repository.mark_file_failed(file_obj.id, error_msg)
            logger.error(f"[FileService] 异步处理异常: file_id={file_obj.id}, error={e}", exc_info=True)
            raise

    def _handle_async_task_completion(self, future, file_id: int) -> None:
        """
        处理异步任务完成

        Args:
            future: 异步任务的Future对象
            file_id: 文件ID
        """
        try:
            # 检查任务是否成功完成
            future.result()  # 如果有异常会在这里抛出
            logger.info(f"[FileService] 异步任务完成: file_id={file_id}")
        except Exception as e:
            logger.error(f"[FileService] 异步任务失败: file_id={file_id}, error={e}")
            # 确保文件状态被正确标记为失败
            try:
                self.file_repository.mark_file_failed(file_id, f"异步任务失败: {str(e)}")
            except Exception as mark_error:
                logger.error(f"[FileService] 标记文件失败状态异常: file_id={file_id}, error={mark_error}")

    def create_file_with_content(
        self,
        context: AuthContext,
        original_filename: str,
        content: str,
        session_id: str,
        file_type: str = FileType.SESSION_FILE.value,
        content_type: Optional[str] = None
    ) -> AlphaFile:
        """
        创建带内容的文件记录（不上传到OSS）

        Args:
            context: 认证上下文
            original_filename: 原始文件名
            content: 文件内容
            session_id: 会话ID
            file_type: 文件类型
            content_type: 文件MIME类型

        Returns:
            AlphaFile: 创建的文件对象
        """
        logger.info(f"[FileService] 创建带内容的文件: {original_filename}, session_id={session_id}, user={context.user_key}")

        try:
            # 生成OSS对象名称（虽然不上传，但保持一致性）
            object_name = self._generate_object_name(original_filename, file_type, str(context.ali_uid))

            # 获取OSS配置
            oss_config = self.oss_service.config

            # 计算内容大小
            content_size = len(content.encode('utf-8')) if content else 0

            # 创建数据库记录
            file_obj = self.file_repository.create_file(
                title=original_filename,
                oss_bucket=oss_config.bucket_name,
                oss_object_name=object_name,
                file_type=file_type,
                session_id=session_id,
                ali_uid=context.ali_uid,
                wy_id=context.wy_id,
                file_size=content_size,
                content_type=content_type or "text/plain",
                content=content
            )

            # 直接标记为完成
            file_obj.mark_completed(content_size)

            logger.info(f"[FileService] 创建带内容的文件成功: file_id={file_obj.id}")
            return file_obj

        except Exception as e:
            logger.error(f"[FileService] 创建带内容的文件失败: {e}", exc_info=True)
            raise

    def register_artifact(
        self,
        ali_uid: str,
        wy_id: str,
        session_id: str,
        artifact_id: str,
        file_name: str,
        file_path: str,
        bucket_name: str,
        is_process_file: bool = False,
        content: Optional[str] = None,
        file_size: Optional[int] = None,
        content_type: Optional[str] = None
    ) -> AlphaFile:
        """
        注册制品文件到文件服务
        
        Args:
            ali_uid: 阿里云用户ID
            wy_id: 魏引用户ID
            session_id: 会话ID
            file_name: 文件名
            file_path: 文件在OSS中的路径
            bucket_name: OSS bucket名称
            is_process_file: 是否是结果制品
            content: 文件内容（可选）
            file_size: 文件大小（可选）
            content_type: 文件MIME类型（可选）
            
        Returns:
            AlphaFile: 创建的文件对象
        """
        logger.info(f"[FileService] 注册制品文件: file_name={file_name}, session_id={session_id}, ali_uid={ali_uid}")
        
        try:
            # 确定文件类型
            file_type = FileType.RESULT_ARTIFACT.value if is_process_file else FileType.PROCESS_ARTIFACT.value
            
            # 如果没有提供文件大小，尝试从OSS获取
            if file_size is None:
                try:
                    object_meta = self.oss_service.get_object_meta(file_path)
                    if object_meta:
                        file_size = object_meta.get('content_length', 0)
                        if not content_type:
                            content_type = object_meta.get('content_type', 'application/octet-stream')
                except Exception as e:
                    logger.warning(f"[FileService] 无法获取文件元数据: {e}")
                    file_size = 0
            
            # 设置默认值
            if not content_type:
                content_type = 'application/octet-stream'
            if file_size is None:
                file_size = 0
                
            # 创建数据库记录
            file_obj = self.file_repository.create_file(
                title=file_name,
                oss_bucket=bucket_name,
                oss_object_name=file_path,
                file_type=file_type,
                session_id=session_id,
                ali_uid=int(ali_uid) if ali_uid else None,
                wy_id=wy_id,
                artifact_id=artifact_id,
                file_size=file_size,
                content_type=content_type,
                content=content,
                upload_status=UploadStatus.COMPLETED.value  # 制品文件认为已经完成上传
            )
            
            # 创建资源权限
            try:
                # 构建认证上下文
                from .auth_service import AuthContext
                context = AuthContext(
                    ali_uid=int(ali_uid),
                    wy_id=wy_id
                )
                auth_service.register_resource(
                    context=context,
                    resource_type=ResourceType.FILE,
                    resource_id=str(file_obj.id),
                    resource_name=file_name,
                    is_public=False
                )
                logger.info(f"[FileService] 制品文件权限创建成功: file_id={file_obj.id}")
            except Exception as e:
                logger.error(f"[FileService] 制品文件权限创建失败: {e}")
                # 权限创建失败不影响文件注册
            
            logger.info(f"[FileService] 制品文件注册成功: file_id={file_obj.id}, file_name={file_name}")
            return file_obj
            
        except Exception as e:
            logger.error(f"[FileService] 制品文件注册失败: {e}", exc_info=True)
            raise

    def update_file_content(self, file_id: int, content: str) -> bool:
        """
        更新文件内容

        Args:
            file_id: 文件ID
            content: 新的文件内容

        Returns:
            bool: 是否更新成功
        """
        try:
            success = self.file_repository.update_file_content(file_id, content)
            if success:
                logger.info(f"[FileService] 更新文件内容成功: file_id={file_id}")
            else:
                logger.warning(f"[FileService] 更新文件内容失败: file_id={file_id}")
            return success
        except Exception as e:
            logger.error(f"[FileService] 更新文件内容异常: file_id={file_id}, error={e}")
            return False
    
    def rename_file(self, context: AuthContext, artifact_id: str, new_file_name: str) -> Optional[Tuple[AlphaFile, str]]:
        """
        重命名文件

        Args:
            context: 认证上下文
            artifact_id: 制品ID
            new_file_name: 新文件名

        Returns:
            Optional[Tuple[AlphaFile, str]]: ((更新后的文件对象, 原文件名))，失败返回none
        """
        try:
            logger.info(f"[FileService] 开始重命名文件: artifact_id={artifact_id}, new_name={new_file_name}, user={context.user_key}")

            # 1. 获取文件信息
            file_obj = self.file_repository.get_file_by_artifact_id(artifact_id)
            if not file_obj:
                logger.warning(f"[FileService] 文件不存在: artifact_id={artifact_id}")
                return None

            # 2. 检查权限（只有文件所有者才能重命名）
            if file_obj.ali_uid != context.ali_uid or file_obj.wy_id != context.wy_id:
                logger.warning(f"[FileService] 无权限重命名文件: artifact_id={artifact_id}, owner={file_obj.ali_uid}:{file_obj.wy_id}, user={context.user_key}")
                return None

            # 3. 验证新文件名
            if not new_file_name or not new_file_name.strip():
                logger.warning(f"[FileService] 新文件名不能为空: artifact_id={artifact_id}")
                return None

            new_file_name = new_file_name.strip()
            if len(new_file_name) > 255:
                logger.warning(f"[FileService] 文件名太长: artifact_id={artifact_id}, length={len(new_file_name)}")
                return None

            # 检查禁止的字符
            forbidden_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
            if any(char in new_file_name for char in forbidden_chars):
                logger.warning(f"[FileService] 文件名包含非法字符: artifact_id={artifact_id}, name={new_file_name}")
                return None

            # 4. 检查是否与原文件名相同
            old_file_name = file_obj.title
            if file_obj.title == new_file_name:
                logger.info(f"[FileService] 文件名未变化: artifact_id={artifact_id}, name={new_file_name}")
                return (file_obj, old_file_name)

            # 5. 更新数据库
            success = self.file_repository.update_file_name(file_obj.id, new_file_name)
            if not success:
                logger.error(f"[FileService] 数据库更新失败: artifact_id={artifact_id}")
                return None

            # 6. 重新获取更新后的文件对象
            updated_file = self.file_repository.get_file_by_id(file_obj.id)
            if updated_file:
                logger.info(f"[FileService] 文件重命名成功: artifact_id={artifact_id}, old_name={old_file_name}, new_name={new_file_name}")
                return (updated_file, old_file_name)
            else:
                logger.error(f"[FileService] 获取更新后的文件对象失败: artifact_id={artifact_id}")
                return None

        except Exception as e:
            logger.error(f"[FileService] 重命名文件异常: artifact_id={artifact_id}, error={e}")
            return None

    def delete_file(self, context: AuthContext, file_id: int, delete_from_oss: bool = True) -> bool:
        """
        删除文件（包括数据库记录、OSS文件和鉴权记录）

        Args:
            context: 认证上下文
            file_id: 文件ID
            delete_from_oss: 是否同时删除OSS中的文件

        Returns:
            bool: 是否删除成功
        """
        try:
            logger.info(f"[FileService] 开始删除文件: file_id={file_id}, user={context.user_key}")

            # 1. 获取文件信息
            file_obj = self.file_repository.get_file_by_id(file_id)
            if not file_obj:
                logger.warning(f"[FileService] 文件不存在: file_id={file_id}")
                return False

            # 2. 检查权限（只有文件所有者才能删除）
            if file_obj.ali_uid != context.ali_uid or file_obj.wy_id != context.wy_id:
                logger.warning(f"[FileService] 无权限删除文件: file_id={file_id}, owner={file_obj.ali_uid}:{file_obj.wy_id}, user={context.user_key}")
                return False

            # 3. 从鉴权系统中注销资源
            try:
                auth_service.unregister_resource(
                    context=context,
                    resource_type=ResourceType.FILE,
                    resource_id=str(file_id)
                )
                logger.info(f"[FileService] 文件鉴权注销成功: file_id={file_id}")
            except Exception as auth_error:
                logger.error(f"[FileService] 文件鉴权注销失败: file_id={file_id}, error={auth_error}")
                # 鉴权注销失败不阻止文件删除流程

            # 4. 删除OSS文件（如果需要）
            if delete_from_oss and file_obj.oss_object_name:
                try:
                    delete_result = self.oss_service.delete_object(file_obj.oss_object_name)
                    if delete_result.success:
                        logger.info(f"[FileService] OSS文件删除成功: file_id={file_id}, object={file_obj.oss_object_name}")
                    else:
                        logger.warning(f"[FileService] OSS文件删除失败: file_id={file_id}, error={delete_result.error_message}")
                        # OSS删除失败不阻止数据库记录删除
                except Exception as oss_error:
                    logger.error(f"[FileService] OSS文件删除异常: file_id={file_id}, error={oss_error}")
                    # OSS删除异常不阻止数据库记录删除

            # 5. 删除数据库记录
            success = self.file_repository.delete_file(file_id)
            if success:
                logger.info(f"[FileService] 文件删除成功: file_id={file_id}")
            else:
                logger.error(f"[FileService] 文件删除失败: file_id={file_id}")

            return success

        except Exception as e:
            logger.error(f"[FileService] 删除文件异常: file_id={file_id}, error={e}")
            return False

    def batch_delete_files(self, context: AuthContext, artifact_ids: List[str], delete_from_oss: bool = True) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        批量删除文件（包括数据库记录、OSS文件和鉴权记录）

        Args:
            context: 认证上下文
            artifact_ids: 制品ID列表
            delete_from_oss: 是否同时删除OSS中的文件

        Returns:
            Tuple[List[Dict], List[Dict]]: (成功删除的文件列表, 删除失败的文件列表)
        """
        try:
            logger.info(f"[FileService] 开始批量删除文件: artifact_ids={artifact_ids}, user={context.user_key}, delete_from_oss={delete_from_oss}")

            deleted_files = []
            failed_files = []

            # 先获取所有文件信息
            files = self.file_repository.get_files_by_artifact_ids(artifact_ids)
            file_map = {file_obj.artifact_id: file_obj for file_obj in files}

            # 处理每个制品ID
            for artifact_id in artifact_ids:
                try:
                    # 检查文件是否存在
                    if artifact_id not in file_map:
                        failed_files.append({
                            "artifact_id": artifact_id,
                            "file_name": "未知",
                            "error": "文件不存在"
                        })
                        continue

                    file_obj = file_map[artifact_id]

                    # 检查权限（只有文件所有者才能删除）
                    if file_obj.ali_uid != context.ali_uid or file_obj.wy_id != context.wy_id:
                        failed_files.append({
                            "artifact_id": artifact_id,
                            "file_name": file_obj.title,
                            "error": "无权限删除该文件"
                        })
                        logger.warning(f"[FileService] 无权限删除文件: artifact_id={artifact_id}, file_id={file_obj.id}, owner={file_obj.ali_uid}:{file_obj.wy_id}, user={context.user_key}")
                        continue

                    # 执行删除操作
                    delete_success = self._delete_single_file(context, file_obj, delete_from_oss)

                    if delete_success:
                        deleted_files.append({
                            "artifact_id": artifact_id,
                            "file_name": file_obj.title
                        })
                        logger.info(f"[FileService] 批量删除单个文件成功: artifact_id={artifact_id}, file_id={file_obj.id}")
                    else:
                        failed_files.append({
                            "artifact_id": artifact_id,
                            "file_name": file_obj.title,
                            "error": "删除操作失败"
                        })
                        logger.error(f"[FileService] 批量删除单个文件失败: artifact_id={artifact_id}, file_id={file_obj.id}")

                except Exception as e:
                    # 单个文件删除异常不影响其他文件的删除
                    file_name = file_map.get(artifact_id, type('obj', (), {'title': '未知'})).title if artifact_id in file_map else "未知"
                    failed_files.append({
                        "artifact_id": artifact_id,
                        "file_name": file_name,
                        "error": f"删除异常: {str(e)}"
                    })
                    logger.error(f"[FileService] 批量删除单个文件异常: artifact_id={artifact_id}, error={e}")

            logger.info(f"[FileService] 批量删除完成: 成功={len(deleted_files)}, 失败={len(failed_files)}")
            return deleted_files, failed_files

        except Exception as e:
            logger.error(f"[FileService] 批量删除文件异常: artifact_ids={artifact_ids}, error={e}")
            # 如果批量删除出现异常，将所有制品ID标记为失败
            failed_files = [
                {
                    "artifact_id": artifact_id,
                    "file_name": "未知",
                    "error": f"批量删除异常: {str(e)}"
                }
                for artifact_id in artifact_ids
            ]
            return [], failed_files

    def _delete_single_file(self, context: AuthContext, file_obj: AlphaFile, delete_from_oss: bool) -> bool:
        """
        删除单个文件的内部方法（用于批量删除）

        Args:
            context: 认证上下文
            file_obj: 文件对象
            delete_from_oss: 是否同时删除OSS中的文件

        Returns:
            bool: 是否删除成功
        """
        try:
            # 1. 从鉴权系统中注销资源
            try:
                auth_service.unregister_resource(
                    context=context,
                    resource_type=ResourceType.FILE,
                    resource_id=str(file_obj.id)
                )
                logger.info(f"[FileService] 文件鉴权注销成功: file_id={file_obj.id}")
            except Exception as auth_error:
                logger.error(f"[FileService] 文件鉴权注销失败: file_id={file_obj.id}, error={auth_error}")
                # 鉴权注销失败不阻止文件删除流程

            # 2. 删除OSS文件（如果需要）
            if delete_from_oss and file_obj.oss_object_name:
                try:
                    delete_result = self.oss_service.delete_object(file_obj.oss_object_name)
                    if delete_result.success:
                        logger.info(f"[FileService] OSS文件删除成功: file_id={file_obj.id}, object={file_obj.oss_object_name}")
                    else:
                        logger.warning(f"[FileService] OSS文件删除失败: file_id={file_obj.id}, error={delete_result.error_message}")
                        # OSS删除失败不阻止数据库记录删除
                except Exception as oss_error:
                    logger.error(f"[FileService] OSS文件删除异常: file_id={file_obj.id}, error={oss_error}")
                    # OSS删除异常不阻止数据库记录删除

            # 3. 删除数据库记录
            success = self.file_repository.delete_file(file_obj.id)
            if success:
                logger.info(f"[FileService] 数据库记录删除成功: file_id={file_obj.id}")
            else:
                logger.error(f"[FileService] 数据库记录删除失败: file_id={file_obj.id}")

            return success

        except Exception as e:
            logger.error(f"[FileService] 删除单个文件异常: file_id={file_obj.id}, error={e}")
            return False

    def update_file_doc_id(self, file_id: int, doc_id: str) -> bool:
        """
        更新文件的RAG文档ID

        Args:
            file_id: 文件ID
            doc_id: RAG解析返回的文档ID

        Returns:
            bool: 是否更新成功
        """
        try:
            success = self.file_repository.update_file_doc_id(file_id, doc_id)
            if success:
                logger.info(f"[FileService] 更新文件doc_id成功: file_id={file_id}, doc_id={doc_id}")
            else:
                logger.warning(f"[FileService] 更新文件doc_id失败: file_id={file_id}")
            return success
        except Exception as e:
            logger.error(f"[FileService] 更新文件doc_id异常: file_id={file_id}, error={e}")
            return False

    def get_file_info(self, file_id: int) -> Optional[Dict[str, Any]]:
        """
        获取文件信息
        
        Args:
            file_id: 文件ID
            
        Returns:
            Optional[Dict[str, Any]]: 文件信息字典
        """
        try:
            file_obj = self.file_repository.get_file_by_id(file_id)
            if not file_obj:
                return None
            
            # 转换为字典
            file_info = file_obj.to_dict()
            
            # 如果文件上传完成，添加下载URL
            if file_obj.upload_status == UploadStatus.COMPLETED.value:
                download_url = self.get_file_download_url(file_obj)
                if download_url:
                    file_info["download_url"] = download_url
            
            return file_info
            
        except Exception as e:
            logger.error(f"[FileService] 获取文件信息失败: file_id={file_id}, error={e}")
            return None
    
    def get_file_download_url(
        self,
        file_obj: AlphaFile,
        expires: int = 3600
    ) -> Optional[str]:
        """
        生成文件下载URL
        
        Args:
            file_obj: 文件对象
            expires: URL过期时间（秒）
            
        Returns:
            str: 下载URL，如果生成失败则返回None
        """
        try:
            # 允许ANALYZING和COMPLETED状态的文件生成下载URL
            if file_obj.upload_status not in [UploadStatus.ANALYZING.value, UploadStatus.COMPLETED.value]:
                logger.warning(f"[FileService] 文件状态不允许生成下载URL: file_id={file_obj.id}, status={file_obj.upload_status}")
                return None

            # 生成预签名URL
            download_url = self.oss_service.generate_presigned_url(
                key=file_obj.oss_object_name,
                expires_in=expires
            )
            
            if download_url:
                logger.info(f"[FileService] 生成下载URL成功: file_id={file_obj.id}")
                return download_url
            else:
                logger.error(f"[FileService] 生成下载URL失败: file_id={file_obj.id}")
                return None
            
        except Exception as e:
            logger.error(f"[FileService] 生成下载URL异常: file_id={file_obj.id}, error={e}")
            return None
    
    def get_session_files(
        self,
        owner_ali_uid: int, 
        owner_wy_id: str,
        session_id: str,
        file_types: Optional[List[str]] = None,
        max_result: int = 100,
        next_token: Optional[str] = None,
    ) -> SessionFilesResponse:
        """
        获取会话文件列表
        
        Args:
            session_id: 会话ID
            file_types: 文件类型过滤列表
            limit: 返回数量限制
            offset: 分页偏移量
            
        Returns:
            Dict[str, Any]: 包含文件列表和统计信息的字典
        """
        # 检查参数
        CheckUtils.parameter_not_null(session_id, "session_id")
        CheckUtils.parameter_not_null(owner_ali_uid, "owner_ali_uid")
        CheckUtils.parameter_not_null(owner_wy_id, "owner_wy_id")

        """解析 next_token"""
        pagination_model = NextTokenUtils.decode(next_token)
        gmt = pagination_model.gmt
        id = pagination_model.id

        # try:
        # 获取文件列表
        files = self.file_repository.list_files_by_session(
            ali_uid=owner_ali_uid, 
            wy_id=owner_wy_id, 
            session_id=session_id, 
            file_types=file_types, 
            limit=max_result + 1, 
            less_than_equal_id=id,
            less_than_equal_gmt_create=gmt)

        """生成 next_token"""
        next_token = None
        if len(files) == max_result + 1:
            last_item = files.pop(len(files) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )
        
        return SessionFilesResponse(
            data=[FileInfoResponse.from_orm_model(obj) for obj in files],
            max_result=len(files),
            next_token=next_token
        )
    

    def get_download_urls(
        self,
        file_ids: List[str],
        expires: int = 3600
    ) -> DownloadUrlsResponse:
        """
        批量获取下载链接
        
        Args:
            file_ids: 文件ID列表
            expires: 链接过期时间（秒）
            
        Returns:
            Dict[str, Any]: 包含成功和失败信息的字典
        """
        from ...application.file_api_models import FailedFile, DownloadLink
        try:
            # 获取文件列表
            files = self.file_repository.get_files_by_ids(file_ids)
            
            download_links = []
            failed_files = []
            
            # 为每个文件生成下载链接
            for file_obj in files:
                try:            
                    if str(file_obj.upload_status) != UploadStatus.COMPLETED.value:
                        failed_files.append({
                            "artifact_id": file_obj.artifact_id,
                            "file_name": file_obj.title,
                            "error": f"文件未上传完成，状态: {file_obj.upload_status}"
                        })
                        continue
                    
                    download_url = self.get_file_download_url(file_obj, expires)
                    if download_url:
                        download_links.append({
                            "artifact_id": file_obj.artifact_id,
                            "file_name": file_obj.title,
                            "file_size": file_obj.file_size,
                            "content_type": file_obj.content_type,
                            "download_url": download_url,
                            "expires_in": expires
                        })
                    else:
                        failed_files.append({
                            "artifact_id": file_obj.artifact_id,
                            "file_name": file_obj.title,
                            "error": "生成下载链接失败"
                        })
                        
                except Exception as e:
                    failed_files.append({
                        "artifact_id": file_obj.artifact_id,
                        "file_name": file_obj.title,
                        "error": f"处理失败: {str(e)}"
                    })
            
            # 处理未找到的文件ID
            found_file_ids = {file_obj.id for file_obj in files}
            for file_id in file_ids:
                if file_id not in found_file_ids:
                    failed_files.append({
                        "file_id": str(file_id),
                        "file_name": "未知",
                        "error": "文件不存在"
                    })
            
            return DownloadUrlsResponse(
                download_links=download_links,
                failed_files=failed_files,
            )
            
        except Exception as e:
            logger.error(f"[FileService] 批量获取下载链接失败: file_ids={file_ids}, error={e}")
            from ...application.file_api_models import FailedFile
            return DownloadUrlsResponse(
                download_links=[],
                failed_files=[FailedFile(artifact_id=str(fid), file_name="未知", error="服务异常") for fid in file_ids],
            )

    def get_download_urls_by_artifact_ids(
        self,
        artifact_ids: List[str],
        expires: int = 3600
    ) -> DownloadUrlsResponse:
        """
        根据制品ID列表批量获取下载链接
        
        Args:
            artifact_ids: 制品ID列表
            expires: 链接过期时间（秒）
            
        Returns:
            DownloadUrlsResponse: 包含成功和失败信息的响应
        """
        from ...application.file_api_models import FailedFile, DownloadLink
        try:
            # 获取文件列表
            files = self.file_repository.get_files_by_artifact_ids(artifact_ids)
            
            download_links = []
            failed_files = []
            
            # 为每个文件生成下载链接
            for file_obj in files:
                try:            
                    if str(file_obj.upload_status) != UploadStatus.COMPLETED.value:
                        failed_files.append({
                            "artifact_id": file_obj.artifact_id,
                            "file_name": file_obj.title,
                            "error": f"文件未上传完成，状态: {file_obj.upload_status}"
                        })
                        continue
                    
                    download_url = self.get_file_download_url(file_obj, expires)
                    if download_url:
                        download_links.append({
                            "artifact_id": file_obj.artifact_id,
                            "file_name": file_obj.title,
                            "file_size": file_obj.file_size,
                            "content_type": file_obj.content_type,
                            "download_url": download_url,
                            "expires_in": expires
                        })
                    else:
                        failed_files.append({
                            "artifact_id": file_obj.artifact_id,
                            "file_name": file_obj.title,
                            "error": "生成下载链接失败"
                        })
                        
                except Exception as e:
                    failed_files.append({
                        "artifact_id": file_obj.artifact_id,
                        "file_name": file_obj.title,
                        "error": f"处理失败: {str(e)}"
                    })
            
            # 处理未找到的制品ID
            found_artifact_ids = {file_obj.artifact_id for file_obj in files if file_obj.artifact_id}
            for artifact_id in artifact_ids:
                if artifact_id not in found_artifact_ids:
                    failed_files.append({
                        "artifact_id": artifact_id,
                        "file_name": "未知",
                        "error": "制品不存在"
                    })
            
            return DownloadUrlsResponse(
                download_links=download_links,
                failed_files=failed_files,
            )
            
        except Exception as e:
            logger.error(f"[FileService] 根据制品ID批量获取下载链接失败: artifact_ids={artifact_ids}, error={e}")
            from ...application.file_api_models import FailedFile
            return DownloadUrlsResponse(
                download_links=[],
                failed_files=[FailedFile(artifact_id=artifact_id, file_name="未知", error="服务异常") for artifact_id in artifact_ids],
            )

    def _generate_object_name(self, original_filename: str, file_type: str, ali_uid: str) -> str:
        """
        生成OSS对象名称

        Args:
            original_filename: 原始文件名
            file_type: 文件类型
            ali_uid: 阿里云用户ID，用于构建存储路径

        Returns:
            str: OSS对象名称
        """
        filename = original_filename

        # 根据文件类型确定存储路径，并在路径中包含ali_uid
        if file_type == FileType.RESULT_ARTIFACT.value:
            return f"artifacts/result/{ali_uid}/{filename}"
        elif file_type == FileType.PROCESS_ARTIFACT.value:
            return f"artifacts/process/{ali_uid}/{filename}"
        else:  # SESSION_FILE
            return f"session_files/{ali_uid}/{filename}"

    def _validate_file(self, file_content: bytes, original_filename: str) -> None:
        """
        验证文件

        Args:
            file_content: 文件内容
            original_filename: 原始文件名

        Raises:
            ValueError: 文件验证失败
        """
        if not original_filename:
            raise ValueError("文件名不能为空")

        file_size = len(file_content)
        max_size = 100 * 1024 * 1024  # 100MB
        if file_size > max_size:
            raise ValueError(f"文件大小超过限制，最大允许{max_size // (1024 * 1024)}MB")

        # 检查文件类型（可以根据需要扩展）
        forbidden_extensions = ['.exe', '.bat', '.cmd', '.scr', '.com']
        file_extension = "." + original_filename.split(".")[-1].lower() if "." in original_filename else ""
        if file_extension in forbidden_extensions:
            raise ValueError(f"不允许上传的文件类型: {file_extension}")

    def trigger_rag_parsing(self, file_id: int, context: AuthContext) -> bool:
        """
        手动触发RAG解析（可用于重试或延迟解析）

        Args:
            file_id: 文件ID
            context: 认证上下文

        Returns:
            bool: 是否成功触发解析
        """
        try:
            file_obj = self.file_repository.get_file_by_id(file_id)
            if not file_obj:
                logger.error(f"[FileService] 触发RAG解析失败：文件不存在: file_id={file_id}")
                return False

            if file_obj.type != FileType.SESSION_FILE.value:
                logger.info(f"[FileService] 跳过RAG解析：非会话文件: file_id={file_id}, type={file_obj.type}")
                return False

            # 检查文件状态，只有completed或analyzing状态的文件才能进行RAG解析
            if file_obj.upload_status not in [UploadStatus.COMPLETED.value, UploadStatus.ANALYZING.value]:
                logger.warning(f"[FileService] 跳过RAG解析：文件状态不正确: file_id={file_id}, status={file_obj.upload_status}")
                return False

            # 如果文件不是analyzing状态，先设置为analyzing
            if file_obj.upload_status != UploadStatus.ANALYZING.value:
                analyzing_success = self.file_repository.mark_file_analyzing(file_id)
                if not analyzing_success:
                    logger.error(f"[FileService] 标记文件为分析中状态失败: file_id={file_id}")
                    return False

            self._parse_document_with_rag(file_obj, context)
            return True

        except Exception as e:
            logger.error(f"[FileService] 触发RAG解析异常: file_id={file_id}, error={e}")
            return False

    def _parse_document_with_rag(self, file_obj: AlphaFile, context: AuthContext) -> None:
        """
        使用RAG客户端解析文档

        Args:
            file_obj: 文件对象
            context: 认证上下文
        """
        try:
            logger.info(f"[FileService] 开始RAG解析文档: file_id={file_obj.id}, filename={file_obj.title}")

            # 再次确认文件状态，确保文件处于分析中状态
            updated_file = self.file_repository.get_file_by_id(file_obj.id)
            if not updated_file:
                logger.error(f"[FileService] RAG解析失败：文件不存在: file_id={file_obj.id}")
                return

            # 检查文件是否处于可分析状态（analyzing 或 completed）
            if updated_file.upload_status not in [UploadStatus.ANALYZING.value, UploadStatus.COMPLETED.value]:
                logger.warning(f"[FileService] RAG解析跳过：文件状态不正确: file_id={file_obj.id}, status={updated_file.upload_status}")
                return

            logger.info(f"[FileService] 文件状态确认，开始RAG解析: file_id={file_obj.id}, status={updated_file.upload_status}, progress={updated_file.upload_progress}%")

            # 使用更新后的文件对象
            file_obj = updated_file

            # 创建RAG客户端
            rag_client = create_rag_client()

            # 生成文件的下载URL
            file_url = self._generate_file_download_url(file_obj)

            # 调用RAG解析
            response = rag_client.parse_document_by_url(
                url=file_url,
                filename=file_obj.title,
                ali_uid=str(context.ali_uid),
                wy_id=context.wy_id
            )

            # 打印返回值到日志
            logger.info(f"[FileService] RAG解析文档成功: file_id={file_obj.id}")
            logger.info(f"[FileService] RAG解析响应: {response}")

            # 提取并保存doc_id
            doc_id = None
            if hasattr(response, 'body') and response.body:
                logger.info(f"[FileService] RAG解析响应体: {response.body}")
                if hasattr(response.body, 'data') and response.body.data:
                    logger.info(f"[FileService] RAG解析数据: {response.body.data}")

                    # 提取doc_id
                    if hasattr(response.body.data, 'doc_id'):
                        doc_id = response.body.data.doc_id
                        logger.info(f"[FileService] 提取到doc_id: {doc_id}")

                        # 保存doc_id到数据库
                        doc_id_updated = self.update_file_doc_id(file_obj.id, doc_id)
                        if doc_id_updated:
                            logger.info(f"[FileService] doc_id保存成功: file_id={file_obj.id}, doc_id={doc_id}")
                        else:
                            logger.error(f"[FileService] doc_id保存失败: file_id={file_obj.id}, doc_id={doc_id}")
                    else:
                        logger.warning(f"[FileService] RAG响应中未找到doc_id: file_id={file_obj.id}")

            # 如果有doc_id，开始监控文档处理状态
            if doc_id:
                logger.info(f"[FileService] 开始监控文档处理状态: file_id={file_obj.id}, doc_id={doc_id}")
                # 注意：在同步版本中，我们直接调用同步版本的监控方法
                self._monitor_document_processing_sync(file_obj.id, doc_id, context)
            else:
                # 没有doc_id，直接标记为完成
                analysis_completed = self.file_repository.mark_file_analysis_completed(file_obj.id)
                if analysis_completed:
                    logger.info(f"[FileService] 文件RAG解析流程完成（无doc_id）: file_id={file_obj.id}")
                else:
                    logger.error(f"[FileService] 标记文件分析完成失败: file_id={file_obj.id}")

        except RagClientError as e:
            logger.error(f"[FileService] RAG解析文档失败: file_id={file_obj.id}, error={e}")
            # RAG解析失败，也要标记为完成状态（避免文件一直处于analyzing状态）
            self.file_repository.mark_file_analysis_completed(file_obj.id)
        except Exception as e:
            logger.error(f"[FileService] RAG解析文档异常: file_id={file_obj.id}, error={e}")
            # RAG解析异常，也要标记为完成状态（避免文件一直处于analyzing状态）
            self.file_repository.mark_file_analysis_completed(file_obj.id)

    async def _parse_document_with_rag_async(self, file_obj: AlphaFile, context: AuthContext) -> None:
        """
        使用RAG客户端异步解析文档

        Args:
            file_obj: 文件对象
            context: 认证上下文
        """
        try:
            logger.info(f"[FileService] 开始RAG解析文档(异步): file_id={file_obj.id}, filename={file_obj.title}")

            # 再次确认文件状态，确保文件处于分析中状态
            updated_file = self.file_repository.get_file_by_id(file_obj.id)
            if not updated_file:
                logger.error(f"[FileService] RAG解析失败：文件不存在: file_id={file_obj.id}")
                return

            # 检查文件是否处于可分析状态（analyzing 或 completed）
            if updated_file.upload_status not in [UploadStatus.ANALYZING.value, UploadStatus.COMPLETED.value]:
                logger.warning(f"[FileService] RAG解析跳过：文件状态不正确: file_id={file_obj.id}, status={updated_file.upload_status}")
                return

            logger.info(f"[FileService] 文件状态确认，开始RAG解析: file_id={file_obj.id}, status={updated_file.upload_status}, progress={updated_file.upload_progress}%")

            # 使用更新后的文件对象
            file_obj = updated_file

            # 创建RAG客户端
            rag_client = create_rag_client()

            # 生成文件的下载URL
            file_url = self._generate_file_download_url(file_obj)

            # 调用RAG解析
            response = rag_client.parse_document_by_url(
                url=file_url,
                filename=file_obj.title,
                ali_uid=str(context.ali_uid),
                wy_id=context.wy_id
            )

            # 打印返回值到日志
            logger.info(f"[FileService] RAG解析文档成功: file_id={file_obj.id}")
            logger.info(f"[FileService] RAG解析响应: {response}")

            # 提取并保存doc_id
            doc_id = None
            if hasattr(response, 'body') and response.body:
                logger.info(f"[FileService] RAG解析响应体: {response.body}")
                if hasattr(response.body, 'data') and response.body.data:
                    logger.info(f"[FileService] RAG解析数据: {response.body.data}")

                    # 提取doc_id
                    if hasattr(response.body.data, 'doc_id'):
                        doc_id = response.body.data.doc_id
                        logger.info(f"[FileService] 提取到doc_id: {doc_id}")

                        # 保存doc_id到数据库
                        doc_id_updated = self.update_file_doc_id(file_obj.id, doc_id)
                        if doc_id_updated:
                            logger.info(f"[FileService] doc_id保存成功: file_id={file_obj.id}, doc_id={doc_id}")
                        else:
                            logger.error(f"[FileService] doc_id保存失败: file_id={file_obj.id}, doc_id={doc_id}")
                    else:
                        logger.warning(f"[FileService] RAG响应中未找到doc_id: file_id={file_obj.id}")

            # 如果有doc_id，开始监控文档处理状态
            if doc_id:
                logger.info(f"[FileService] 开始监控文档处理状态: file_id={file_obj.id}, doc_id={doc_id}")
                # 在异步版本中，我们调用异步版本的监控方法
                await self._monitor_document_processing(file_obj.id, doc_id, context)
            else:
                # 没有doc_id，直接标记为完成
                analysis_completed = self.file_repository.mark_file_analysis_completed(file_obj.id)
                if analysis_completed:
                    logger.info(f"[FileService] 文件RAG解析流程完成（无doc_id）: file_id={file_obj.id}")
                else:
                    logger.error(f"[FileService] 标记文件分析完成失败: file_id={file_obj.id}")

        except RagClientError as e:
            logger.error(f"[FileService] RAG解析文档失败: file_id={file_obj.id}, error={e}")
            # RAG解析失败，也要标记为完成状态（避免文件一直处于analyzing状态）
            self.file_repository.mark_file_analysis_completed(file_obj.id)
        except Exception as e:
            logger.error(f"[FileService] RAG解析文档异常: file_id={file_obj.id}, error={e}")
            # RAG解析异常，也要标记为完成状态（避免文件一直处于analyzing状态）
            self.file_repository.mark_file_analysis_completed(file_obj.id)

    def _generate_file_download_url(self, file_obj: AlphaFile) -> str:
        """
        生成文件下载URL

        Args:
            file_obj: 文件对象

        Returns:
            str: 文件下载URL
        """
        try:
            # 刷新文件对象状态，确保获取最新的上传状态
            updated_file = self.file_repository.get_file_by_id(file_obj.id)
            if updated_file:
                file_obj = updated_file

            # 使用现有的方法生成下载URL
            download_url = self.get_file_download_url(file_obj, expires=3600)

            if download_url:
                logger.info(f"[FileService] 生成文件下载URL成功: file_id={file_obj.id}")
                return download_url
            else:
                # 如果生成预签名URL失败，返回一个备用URL
                backup_url = f"https://{file_obj.oss_bucket}.oss-cn-hangzhou.aliyuncs.com/{file_obj.oss_object_name}"
                logger.warning(f"[FileService] 使用备用URL: file_id={file_obj.id}, url={backup_url}")
                return backup_url

        except Exception as e:
            logger.error(f"[FileService] 生成文件下载URL失败: file_id={file_obj.id}, error={e}")
            # 如果生成预签名URL失败，返回一个备用URL
            return f"https://{file_obj.oss_bucket}.oss-cn-hangzhou.aliyuncs.com/{file_obj.oss_object_name}"

    def _monitor_document_processing_sync(self, file_id: int, doc_id: str, context: AuthContext) -> None:
        """
        监控文档处理状态，等待chunk_parse完成（同步版本）

        Args:
            file_id: 文件ID
            doc_id: 文档ID
            context: 认证上下文
        """
        import time

        try:
            logger.info(f"[FileService] 开始监控文档处理: file_id={file_id}, doc_id={doc_id}")

            # 创建RAG客户端
            rag_client = create_rag_client()

            max_attempts = 300
            check_interval = 0.1  # 100毫秒
            start_time = time.time()

            for attempt in range(1, max_attempts + 1):
                try:
                    logger.info(f"[FileService] 检查文档状态 (第{attempt}次): doc_id={doc_id}")

                    # 调用check_docs_status
                    response = rag_client.check_docs_status(doc_ids=[doc_id])

                    logger.info(f"[FileService] 文档状态检查响应: {response}")

                    # 解析响应
                    if hasattr(response, 'body') and response.body and hasattr(response.body, 'data'):
                        docs_data = response.body.data
                        if docs_data and len(docs_data) > 0:
                            doc_info = docs_data[0]  # 取第一个文档的信息

                            # 检查stages
                            if hasattr(doc_info, 'stages') and doc_info.stages:
                                stages = doc_info.stages
                                logger.info(f"[FileService] 文档处理阶段: {stages}")

                                # 检查是否包含chunk_parse
                                if 'chunk_parse' in stages:
                                    logger.info(f"[FileService] 文档已完成chunk_parse阶段: doc_id={doc_id}")

                                    # 获取chunk_parse路径
                                    if hasattr(doc_info, 'file_paths') and doc_info.file_paths:
                                        file_paths = doc_info.file_paths
                                        if hasattr(file_paths, 'chunk_parse'):
                                            chunk_parse_path = file_paths.chunk_parse
                                            logger.info(f"[FileService] 获取到chunk_parse路径: {chunk_parse_path}")

                                            # 下载并保存chunk_parse内容
                                            success = self._download_and_save_chunk_parse_content(
                                                file_id, doc_id, chunk_parse_path
                                            )

                                            if success:
                                                # 标记文件处理完成
                                                self.file_repository.mark_file_analysis_completed(file_id)
                                                total_wait_time = time.time() - start_time
                                                logger.info(f"[FileService] 文档处理完全完成: file_id={file_id}, doc_id={doc_id}, 总等待时间={total_wait_time:.2f}秒")
                                            else:
                                                logger.error(f"[FileService] chunk_parse内容保存失败: file_id={file_id}")

                                            return


                    if attempt < max_attempts:
                        # 使用 time.sleep 实现阻塞等待（同步版本）
                        time.sleep(check_interval)

                except Exception as e:
                    logger.error(f"[FileService] 检查文档状态异常 (第{attempt}次): doc_id={doc_id}, error={e}")
                    if attempt < max_attempts:
                        # 使用 time.sleep 实现阻塞等待（同步版本）
                        time.sleep(check_interval)
                    continue

            # 超时未完成
            total_wait_time = time.time() - start_time
            logger.error(f"[FileService] 文档处理监控超时: file_id={file_id}, doc_id={doc_id}, 总等待时间={total_wait_time:.2f}秒")
            # 即使超时也要标记为完成，避免文件一直处于analyzing状态
            self.file_repository.mark_file_analysis_completed(file_id)

        except Exception as e:
            total_wait_time = time.time() - start_time if 'start_time' in locals() else 0
            logger.error(f"[FileService] 文档处理监控异常: file_id={file_id}, doc_id={doc_id}, error={e}, 总等待时间={total_wait_time:.2f}秒")
            # 发生异常也要标记为完成
            self.file_repository.mark_file_analysis_completed(file_id)

    async def _monitor_document_processing(self, file_id: int, doc_id: str, context: AuthContext) -> None:
        """
        监控文档处理状态，等待chunk_parse完成（异步版本）

        Args:
            file_id: 文件ID
            doc_id: 文档ID
            context: 认证上下文
        """
        import time

        try:
            logger.info(f"[FileService] 开始监控文档处理: file_id={file_id}, doc_id={doc_id}")

            # 创建RAG客户端
            rag_client = create_rag_client()

            max_attempts = 300
            check_interval = 0.1  # 100毫秒
            start_time = time.time()

            for attempt in range(1, max_attempts + 1):
                try:
                    logger.info(f"[FileService] 检查文档状态 (第{attempt}次): doc_id={doc_id}")

                    # 调用check_docs_status
                    response = rag_client.check_docs_status(doc_ids=[doc_id])

                    logger.info(f"[FileService] 文档状态检查响应: {response}")

                    # 解析响应
                    if hasattr(response, 'body') and response.body and hasattr(response.body, 'data'):
                        docs_data = response.body.data
                        if docs_data and len(docs_data) > 0:
                            doc_info = docs_data[0]  # 取第一个文档的信息

                            # 检查stages
                            if hasattr(doc_info, 'stages') and doc_info.stages:
                                stages = doc_info.stages
                                logger.info(f"[FileService] 文档处理阶段: {stages}")

                                # 检查是否包含chunk_parse
                                if 'chunk_parse' in stages:
                                    logger.info(f"[FileService] 文档已完成chunk_parse阶段: doc_id={doc_id}")

                                    # 获取chunk_parse路径
                                    if hasattr(doc_info, 'file_paths') and doc_info.file_paths:
                                        file_paths = doc_info.file_paths
                                        if hasattr(file_paths, 'chunk_parse'):
                                            chunk_parse_path = file_paths.chunk_parse
                                            logger.info(f"[FileService] 获取到chunk_parse路径: {chunk_parse_path}")

                                            # 下载并保存chunk_parse内容
                                            success = self._download_and_save_chunk_parse_content(
                                                file_id, doc_id, chunk_parse_path
                                            )

                                            if success:
                                                # 标记文件处理完成
                                                self.file_repository.mark_file_analysis_completed(file_id)
                                                total_wait_time = time.time() - start_time
                                                logger.info(f"[FileService] 文档处理完全完成: file_id={file_id}, doc_id={doc_id}, 总等待时间={total_wait_time:.2f}秒")
                                            else:
                                                logger.error(f"[FileService] chunk_parse内容保存失败: file_id={file_id}")

                                            return


                    if attempt < max_attempts:
                        # 使用 asyncio.sleep 替代 time.sleep 实现非阻塞等待
                        await asyncio.sleep(check_interval)

                except Exception as e:
                    logger.error(f"[FileService] 检查文档状态异常 (第{attempt}次): doc_id={doc_id}, error={e}")
                    if attempt < max_attempts:
                        # 使用 asyncio.sleep 替代 time.sleep 实现非阻塞等待
                        await asyncio.sleep(check_interval)
                    continue

            # 超时未完成
            total_wait_time = time.time() - start_time
            logger.error(f"[FileService] 文档处理监控超时: file_id={file_id}, doc_id={doc_id}, 总等待时间={total_wait_time:.2f}秒")
            # 即使超时也要标记为完成，避免文件一直处于analyzing状态
            self.file_repository.mark_file_analysis_completed(file_id)

        except Exception as e:
            total_wait_time = time.time() - start_time if 'start_time' in locals() else 0
            logger.error(f"[FileService] 文档处理监控异常: file_id={file_id}, doc_id={doc_id}, error={e}, 总等待时间={total_wait_time:.2f}秒")
            # 发生异常也要标记为完成
            self.file_repository.mark_file_analysis_completed(file_id)

    def _download_and_save_chunk_parse_content(self, file_id: int, doc_id: str, chunk_parse_path: str) -> bool:
        """
        下载chunk_parse内容并保存到数据库

        Args:
            file_id: 文件ID
            doc_id: 文档ID
            chunk_parse_path: chunk_parse文件路径

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"[FileService] 开始下载chunk_parse内容: file_id={file_id}, path={chunk_parse_path}")

            # 方法1：尝试使用RAG专用的OSS客户端直接下载
            chunk_parse_content = self.oss_service.download_rag_file_content(chunk_parse_path)

            if chunk_parse_content:
                logger.info(f"[FileService] 使用RAG OSS客户端下载成功: file_id={file_id}, 内容长度={len(chunk_parse_content)}")
            else:
                # 方法2：如果直接下载失败，尝试使用预签名URL
                logger.info(f"[FileService] 直接下载失败，尝试使用预签名URL")
                presigned_url = self.oss_service.generate_rag_presigned_url(chunk_parse_path, expires_in=3600)

                if presigned_url:
                    logger.info(f"[FileService] RAG预签名URL生成成功, 开始下载: {presigned_url}")
                    response = requests.get(presigned_url, timeout=30)

                    if response.status_code == 200:
                        chunk_parse_content = response.text
                        logger.info(f"[FileService] 使用预签名URL下载成功: file_id={file_id}, 内容长度={len(chunk_parse_content)}")
                    else:
                        logger.error(f"[FileService] 预签名URL下载失败: file_id={file_id}, status_code={response.status_code}")
                        return False
                else:
                    logger.error(f"[FileService] 生成预签名URL失败: file_id={file_id}")
                    return False

            # 处理下载到的内容
            if chunk_parse_content:
                logger.info(f"chunk_parse_content = {chunk_parse_content}")

                # 解析chunk_parse JSON内容，提取有用信息
                try:
                    import json
                    chunk_data = json.loads(chunk_parse_content)

                    # 提取文档的分块内容，通常chunk_parse包含文档的分段信息
                    processed_content = self._extract_content_from_chunk_parse(chunk_data)

                    # 保存处理后的内容到数据库
                    success = self.update_file_content(file_id, processed_content)
                    if success:
                        logger.info(f"[FileService] chunk_parse内容保存成功: file_id={file_id}, doc_id={doc_id}")
                        return True
                    else:
                        logger.error(f"[FileService] chunk_parse内容保存失败: file_id={file_id}")
                        return False

                except json.JSONDecodeError as e:
                    logger.error(f"[FileService] chunk_parse JSON解析失败: file_id={file_id}, error={e}")
                    # 如果JSON解析失败，直接保存原始内容
                    success = self.update_file_content(file_id, chunk_parse_content)
                    return success

            else:
                logger.error(f"[FileService] chunk_parse内容下载失败: file_id={file_id}")
                return False

        except Exception as e:
            logger.error(f"[FileService] 下载chunk_parse内容异常: file_id={file_id}, error={e}")
            return False

    def _extract_content_from_chunk_parse(self, chunk_data: dict) -> str:
        """
        从chunk_parse数据中提取有用的内容

        Args:
            chunk_data: chunk_parse的JSON数据

        Returns:
            str: 提取的文档内容
        """
        try:
            # chunk_parse通常包含文档的分段信息
            # 这里根据实际的数据结构来提取内容

            if isinstance(chunk_data, dict):
                # 尝试提取常见的内容字段
                content_parts = []

                # 如果有chunks字段
                if 'chunks' in chunk_data:
                    chunks = chunk_data['chunks']
                    if isinstance(chunks, list):
                        for chunk in chunks:
                            if isinstance(chunk, dict) and 'content' in chunk:
                                content_parts.append(chunk['content'])
                            elif isinstance(chunk, str):
                                content_parts.append(chunk)

                # 如果有content字段
                elif 'content' in chunk_data:
                    content = chunk_data['content']
                    if isinstance(content, str):
                        content_parts.append(content)
                    elif isinstance(content, list):
                        content_parts.extend([str(item) for item in content])

                # 如果有text字段
                elif 'text' in chunk_data:
                    text = chunk_data['text']
                    if isinstance(text, str):
                        content_parts.append(text)

                # 合并所有内容
                if content_parts:
                    extracted_content = '\n\n'.join(content_parts)
                    logger.info(f"[FileService] 从chunk_parse提取内容成功，长度: {len(extracted_content)}")
                    return extracted_content
                else:
                    # 如果没有找到预期的字段，返回JSON字符串
                    logger.warning(f"[FileService] 未找到预期的内容字段，返回原始JSON")
                    return json.dumps(chunk_data, ensure_ascii=False, indent=2)
            else:
                logger.warning(f"[FileService] chunk_parse数据格式异常，返回字符串形式")
                return str(chunk_data)

        except Exception as e:
            logger.error(f"[FileService] 提取chunk_parse内容异常: {e}")
            return json.dumps(chunk_data, ensure_ascii=False, indent=2)

    def shutdown(self, wait: bool = True) -> None:
        """
        关闭文件服务，清理资源

        Args:
            wait: 是否等待正在执行的任务完成
        """
        logger.info("[FileService] 开始关闭文件服务...")

        with self._shutdown_lock:
            if not self._thread_pool._shutdown:
                self._thread_pool.shutdown(wait=wait)
                logger.info(f"[FileService] 线程池已关闭 (wait={wait})")

        # 关闭OSS服务
        try:
            self.oss_service.close()
            logger.info("[FileService] OSS服务已关闭")
        except Exception as e:
            logger.error(f"[FileService] 关闭OSS服务异常: {e}")

        logger.info("[FileService] 文件服务关闭完成")

    async def create_presigned_upload(
        self,
        context: AuthContext,
        file_name: str,
        file_size: int,
        file_type: str,
        agent_id: str,
        session_id: Optional[str] = None,
        upload_file_type: str = "sessionFile"
    ) -> Tuple[AlphaFile, str, int, Dict[str, str]]:
        """
        创建预签名上传

        Args:
            context: 用户认证上下文
            file_name: 文件名
            file_size: 文件大小
            file_type: 文件扩展名/类型
            session_id: 会话ID
            upload_file_type: 上传文件类型
            agent_id: AgentId


        Returns:
            (文件对象, 预签名URL, 过期时间秒数, 上传headers)
        """
        try:
            logger.info(f"[FileService] 创建预签名上传: user={context.user_key}, file={file_name}, size={file_size}")

            # 1. 如果没有session_id，通过session_service创建一个新会话
            if not session_id:
                from .session_service import session_service
                session = await session_service.get_or_create_session_domain(
                    session_id=None,
                    ali_uid=context.ali_uid,
                    agent_id=agent_id or "upload",
                    wy_id=context.wy_id
                )
                session_id = session.session_id

            # 2. 生成OSS对象键
            object_key = self._generate_object_name(
                original_filename=file_name,
                file_type=upload_file_type,
                ali_uid=str(context.ali_uid)
            )

            # 3. 创建文件记录（状态为pending）
            oss_config = oss_service.config
            file_obj = file_repository.create_file(
                title=file_name,
                oss_bucket=oss_config.bucket_name,
                oss_object_name=object_key,
                file_type=upload_file_type,
                session_id=session_id,
                ali_uid=context.ali_uid,
                wy_id=context.wy_id,
                file_size=file_size,
                content_type=self._get_content_type(file_name)
            )
            logger.info(f"[FileService] 文件记录创建成功: file_id={file_obj.id}")

            # 5. 注册到鉴权系统
            try:
                auth_service.register_resource(
                    context=context,
                    resource_type=ResourceType.FILE,
                    resource_id=str(file_obj.id),
                    resource_name=file_name,
                    is_public=False
                )
                logger.info(f"[FileService] 文件鉴权注册成功: file_id={file_obj.id}")
            except Exception as auth_error:
                logger.error(f"[FileService] 文件鉴权注册失败: file_id={file_obj.id}, error={auth_error}")
                # 鉴权注册失败不影响主流程

            # 6. 生成预签名上传URL
            expires = 900  # 15分钟
            upload_url, upload_headers = oss_service.generate_presigned_upload_url(
                object_key=object_key,
                expires=expires,
                content_type=self._get_content_type(file_name),
                content_length=file_size
            )

            logger.info(f"[FileService] 预签名上传创建成功: file_id={file_obj.id}, expires={expires}s")
            return file_obj, upload_url, expires, upload_headers

        except Exception as e:
            logger.error(f"[FileService] 创建预签名上传失败: user={context.user_key}, error={e}")
            raise

    async def confirm_presigned_upload(
        self,
        context: AuthContext,
        file_id: int,
        etag: Optional[str] = None
    ) -> bool:
        """
        确认预签名上传完成

        Args:
            context: 用户认证上下文
            file_id: 文件ID
            etag: 文件ETag（可选）

        Returns:
            是否成功
        """
        try:
            logger.info(f"[FileService] 确认预签名上传: user={context.user_key}, file_id={file_id}")

            # 1. 获取文件记录
            file_obj = file_repository.get_file_by_id(file_id)
            if not file_obj:
                logger.error(f"[FileService] 文件不存在: file_id={file_id}")
                return False

            # 2. 验证用户权限
            if file_obj.ali_uid != context.ali_uid or file_obj.wy_id != context.wy_id:
                logger.error(f"[FileService] 用户无权限确认上传: file_id={file_id}, user={context.user_key}")
                return False

            # 3. 检查文件是否已上传到OSS
            if not oss_service.check_object_exists(file_obj.oss_object_name):
                logger.error(f"[FileService] OSS中文件不存在: file_id={file_id}, oss_object_name={file_obj.oss_object_name}")
                return False

            # 4. 获取OSS对象元数据
            actual_file_size = file_obj.file_size
            meta = oss_service.get_object_meta(file_obj.oss_object_name)
            if meta:
                # 更新文件大小（以OSS中的实际大小为准）
                if meta.get('content_length'):
                    actual_file_size = meta['content_length']

            # 5. 更新文件状态为已完成
            success = file_repository.update_file_status(
                file_id=file_id,
                status=UploadStatus.COMPLETED.value,
                file_size=actual_file_size
            )
            logger.info(f"[FileService] 更新状态成功: file_id={file_id}")
            if not success:
                logger.error(f"[FileService] 更新文件状态失败: file_id={file_id}")
                return False

            # 6. 重新获取更新后的文件对象
            file_obj = file_repository.get_file_by_id(file_id)

            logger.info(f"[FileService] 启动rag处理: file_id={file_id}")
            # 7. 启动异步RAG处理
            await self._start_async_rag_processing(file_obj, context)

            logger.info(f"[FileService] 预签名上传确认成功: file_id={file_id}")
            return True

        except Exception as e:
            logger.error(f"[FileService] 确认预签名上传失败: file_id={file_id}, error={e}")
            return False

    def _start_sync_rag_processing(self, file_obj: AlphaFile, context: AuthContext) -> None:
        """
        启动同步RAG处理（用于预签名上传确认后）

        Args:
            file_obj: 文件对象
            context: 用户认证上下文
        """
        try:
            logger.info(f"[FileService] 启动同步RAG处理: file_id={file_obj.id}, type={file_obj.type}")

            # 根据文件类型决定是否需要RAG处理
            if file_obj.type == FileType.SESSION_FILE.value:
                # 会话文件需要RAG解析
                logger.info(f"[FileService] 会话文件开始RAG解析: file_id={file_obj.id}")

                # 标记为分析中状态
                analyzing_success = self.file_repository.mark_file_analyzing(file_obj.id)
                if analyzing_success:
                    # 获取最新的文件对象并触发RAG解析
                    updated_file = self.file_repository.get_file_by_id(file_obj.id)
                    if updated_file:
                        # 同步执行RAG解析
                        self._parse_document_with_rag(updated_file, context)
                        logger.info(f"[FileService] RAG解析完成: file_id={file_obj.id}")
                    else:
                        logger.error(f"[FileService] 获取文件对象失败: file_id={file_obj.id}")
                        self.file_repository.mark_file_failed(file_obj.id, "获取文件对象失败")
                else:
                    logger.error(f"[FileService] 标记文件为分析中状态失败: file_id={file_obj.id}")
                    self.file_repository.mark_file_failed(file_obj.id, "标记分析状态失败")
            else:
                # 其他文件类型：直接标记为完成
                logger.info(f"[FileService] 非会话文件，直接标记为完成: file_id={file_obj.id}")
                # 注意：这里不需要再次调用mark_file_completed，因为在confirm_presigned_upload中已经更新了状态

        except Exception as e:
            logger.error(f"[FileService] 启动同步RAG处理失败: file_id={file_obj.id}, error={e}")
            # 标记文件处理失败
            try:
                self.file_repository.mark_file_failed(file_obj.id, f"RAG处理启动失败: {str(e)}")
            except Exception as mark_error:
                logger.error(f"[FileService] 标记文件失败状态异常: file_id={file_obj.id}, error={mark_error}")

    async def _start_async_rag_processing(self, file_obj: AlphaFile, context: AuthContext) -> None:
        """
        启动异步RAG处理（用于预签名上传确认后）

        Args:
            file_obj: 文件对象
            context: 用户认证上下文
        """
        try:
            logger.info(f"[FileService] 启动异步RAG处理: file_id={file_obj.id}, type={file_obj.type}")

            # 根据文件类型决定是否需要RAG处理
            if file_obj.type == FileType.SESSION_FILE.value:
                # 会话文件需要RAG解析
                logger.info(f"[FileService] 会话文件开始RAG解析: file_id={file_obj.id}")

                # 标记为分析中状态
                analyzing_success = self.file_repository.mark_file_analyzing(file_obj.id)
                if analyzing_success:
                    # 获取最新的文件对象并触发RAG解析
                    updated_file = self.file_repository.get_file_by_id(file_obj.id)
                    if updated_file:
                        # 异步执行RAG解析
                        await self._parse_document_with_rag_async(updated_file, context)
                        logger.info(f"[FileService] RAG解析完成: file_id={file_obj.id}")
                    else:
                        logger.error(f"[FileService] 获取文件对象失败: file_id={file_obj.id}")
                        self.file_repository.mark_file_failed(file_obj.id, "获取文件对象失败")
                else:
                    logger.error(f"[FileService] 标记文件为分析中状态失败: file_id={file_obj.id}")
                    self.file_repository.mark_file_failed(file_obj.id, "标记分析状态失败")
            else:
                # 其他文件类型：直接标记为完成
                logger.info(f"[FileService] 非会话文件，直接标记为完成: file_id={file_obj.id}")
                # 注意：这里不需要再次调用mark_file_completed，因为在confirm_presigned_upload中已经更新了状态

        except Exception as e:
            logger.error(f"[FileService] 启动异步RAG处理失败: file_id={file_obj.id}, error={e}")
            # 标记文件处理失败
            try:
                self.file_repository.mark_file_failed(file_obj.id, f"RAG处理启动失败: {str(e)}")
            except Exception as mark_error:
                logger.error(f"[FileService] 标记文件失败状态异常: file_id={file_obj.id}, error={mark_error}")


    def _is_preview_supported(self, filename: str) -> bool:
        """
        检查文件是否支持预览
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否支持预览
        """
        if not filename:
            return False
            
        # 提取文件扩展名（转为小写）
        from pathlib import Path
        ext = Path(filename).suffix.lower().lstrip('.')
        
        # 支持预览的文件格式列表
        preview_formats = {
            'doc', 'dot', 'wps', 'wpt', 'docx', 'dotx', 'docm', 'dotm', 'rtf',
            'ppt', 'pptx', 'pptm', 'ppsx', 'ppsm', 'pps', 'potx', 'potm', 'dpt', 'dps',
            'xls', 'xlt', 'et', 'xlsx', 'xltx', 'csv', 'xlsm', 'xltm',
            'pdf'
        }
        
        return ext in preview_formats
    

    def get_artifact_preview_url(self, context: AuthContext, artifact_id: str, expires: int = 3600) -> Tuple[AlphaFile, str, str, int]:
        """
        根据制品ID获取文件预览链接或下载链接

        Args:
            context: 认证上下文
            artifact_id: 制品ID
            expires: 链接过期时间（秒），默认1小时

        Returns:
            Tuple[AlphaFile, str, str, int]: (文件对象, 链接URL, 链接类型, 过期时间)
            
        Raises:
            FileProcessError: 文件处理异常（包含具体错误信息）
                - 文件不存在
                - 无权限访问文件
                - 文件状态异常（如未完成上传）
                - OSS相关错误（如缺少对象名称、生成链接失败）
                - 其他文件处理异常
        """
        try:
            logger.info(f"[FileService] 获取文件链接: artifact_id={artifact_id}, user={context.user_key}")

            # 1. 根据制品ID获取文件
            file_obj = self.file_repository.get_file_by_artifact_id(artifact_id)
            if not file_obj:
                logger.warning(f"[FileService] 文件不存在: artifact_id={artifact_id}")
                raise FileProcessError(f"文件不存在，或已被删除")

            # 2. 检查权限（只有文件所有者才能获取链接）
            if file_obj.ali_uid != context.ali_uid or file_obj.wy_id != context.wy_id:
                logger.warning(f"[FileService] 无权限访问文件: artifact_id={artifact_id}, owner={file_obj.ali_uid}:{file_obj.wy_id}, user={context.user_key}")
                raise FileProcessError(f"当前用户无权限访问文件")

            # 3. 检查文件状态（只有已完成的文件才能访问）
            if file_obj.upload_status != UploadStatus.COMPLETED.value:
                logger.warning(f"[FileService] 文件未完成上传，无法访问: artifact_id={artifact_id}, status={file_obj.upload_status}")
                raise FileProcessError(f"文件未完成上传，无法访问")

            # 4. 检查OSS对象名称
            if not file_obj.oss_object_name:
                logger.warning(f"[FileService] 文件缺少OSS对象名称: artifact_id={artifact_id}")
                raise FileProcessError(f"文件缺少OSS对象名称")

            # 5. 检查文件是否支持预览
            is_preview_supported = self._is_preview_supported(str(file_obj.title))

            try:
                if is_preview_supported:
                    # 支持预览，生成预览链接
                    url = self.oss_service.generate_presigned_url_with_preview(
                        key=str(file_obj.oss_object_name),
                        expires_in=expires
                    )
                    url_type = "preview"
                    logger.info(f"[FileService] 生成预览链接成功: artifact_id={artifact_id}, expires_in={expires}s")
                else:
                    # 不支持预览，生成下载链接
                    url = self.oss_service.generate_presigned_url(
                        key=str(file_obj.oss_object_name),
                        expires_in=expires
                    )
                    url_type = "download"
                    logger.info(f"[FileService] 生成下载链接成功: artifact_id={artifact_id}, expires_in={expires}s")
                
                # 确保返回的 url 为 str，而不是 None；否则抛出异常由上层处理为失败
                if not url:
                    raise FileProcessError(f"生成有效链接失败")

                return file_obj, url, url_type, expires

            except Exception as oss_error:
                logger.error(f"[FileService] 生成链接失败: artifact_id={artifact_id}, error={oss_error}")
                raise FileProcessError(f"生成链接失败: {str(oss_error)}")

        except FileProcessError:
            # 重新抛出我们定义的文件处理异常
            raise
        except Exception as e:
            logger.error(f"[FileService] 获取文件链接异常: artifact_id={artifact_id}, error={e}")
            raise FileProcessError(f"获取文件链接异常: {str(e)}")


    def _get_content_type(self, filename: str) -> str:
        """根据文件名获取MIME类型"""
        import mimetypes
        content_type, _ = mimetypes.guess_type(filename)
        return content_type or 'application/octet-stream'


    def set_files_kb_relationship(self, files: List, session_id: str, kb_id: Optional[str] = None) -> None:
        """
        为文件列表设置知识库关系字段

        Args:
            files: 文件信息列表（FileInfoResponse对象）
            session_id: 会话ID
            kb_id: 知识库ID，如果为空则所有文件的is_in_kb都设置为False
        """
        if not files:
            logger.debug("[FileService] 文件列表为空，跳过知识库关系设置")
            return

        if not kb_id:
            # 如果没有提供kb_id，为所有文件设置is_in_kb为False
            for file_info in files:
                file_info.is_in_kb = False
            logger.debug(f"[FileService] 未提供kb_id，为 {len(files)} 个文件设置is_in_kb=False")
            return

        try:
            # 提取所有文件ID
            file_id_list = [file_info.file_id for file_info in files]
            logger.info(f"[FileService] 检查 {len(file_id_list)} 个文件是否属于知识库: kb_id={kb_id}, session_id={session_id}")

            # 调用知识库服务判断文件是否属于知识库
            from .knowledge_service import knowledgebase_service
            kb_file_results = knowledgebase_service.is_knowledge_base_file(
                kb_id=kb_id,
                session_id=session_id,
                file_id_list=file_id_list
            )

            logger.info(f"[FileService] 知识库文件判断结果: {len(kb_file_results)} 个文件有结果")

            # 为每个文件设置is_in_kb字段
            for file_info in files:
                file_info.is_in_kb = kb_file_results.get(file_info.file_id, False)

            logger.info(f"[FileService] 已为所有文件设置is_in_kb字段")

        except Exception as e:
            logger.error(f"[FileService] 知识库文件判断失败: kb_id={kb_id}, session_id={session_id}, error={e}")
            # 如果判断失败，为所有文件设置is_in_kb为False
            for file_info in files:
                file_info.is_in_kb = False


# 创建全局实例
file_service = FileService()
